/*
Theme Name: Rookie
*/

.posted-on {
	float: right;
}

.search-form .search-field {
	border-right-width: 1px;
	border-left-width: 0;
	border-radius: 3px;
	border-top-left-radius: 0;
	border-bottom-left-radius: 0;
}

.search-form .search-submit {
	border-radius: 3px;
	border-top-right-radius: 0;
	border-bottom-right-radius: 0;
}

.sp-template-countdown time span {
	border-right: 0;
	border-left: 1px solid #e0e0e0;
}

.sp-template-countdown time span:first-child {
	border-right: 1px solid #e0e0e0;
}

@media screen and (min-width: 601px) {
	.site-logo {
		margin: -1em 0 -1em 10px;
	}

	.content-area,
	.widecolumn {
		box-shadow: -1px 0 0 #e0e0e0;
	}

	.site-footer .footer-widget-region {
		padding-right: 0;
		padding-left: 20px;
	}

	.site-footer .footer-widget-region:first-child {
		padding-left: 20px;
		padding-right: 20px;
	}

	.site-footer-has-logo .footer-widget-region:first-child {
		padding-right: 0;
	}
}
