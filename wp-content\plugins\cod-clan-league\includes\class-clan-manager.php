<?php
/**
 * Clan management functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class COD_Clan_League_Clan_Manager {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('wp_ajax_cod_create_clan', array($this, 'ajax_create_clan'));
        add_action('wp_ajax_cod_invite_member', array($this, 'ajax_invite_member'));
        add_action('wp_ajax_cod_respond_invitation', array($this, 'ajax_respond_invitation'));
        add_action('wp_ajax_cod_remove_member', array($this, 'ajax_remove_member'));
        add_action('wp_ajax_cod_leave_clan', array($this, 'ajax_leave_clan'));
        
        add_action('init', array($this, 'handle_invitation_response'));
    }
    
    /**
     * Create a new clan
     */
    public function create_clan($data) {
        global $wpdb;
        
        // Validate required fields
        if (empty($data['name']) || empty($data['tag']) || empty($data['captain_id'])) {
            return new WP_Error('missing_data', __('Missing required clan information.', 'cod-clan-league'));
        }
        
        // Validate clan name and tag uniqueness
        $clans_table = $wpdb->prefix . 'cod_clans';
        
        $existing_name = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $clans_table WHERE name = %s",
            $data['name']
        ));
        
        if ($existing_name) {
            return new WP_Error('name_exists', __('A clan with this name already exists.', 'cod-clan-league'));
        }
        
        $existing_tag = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $clans_table WHERE tag = %s",
            $data['tag']
        ));
        
        if ($existing_tag) {
            return new WP_Error('tag_exists', __('A clan with this tag already exists.', 'cod-clan-league'));
        }
        
        // Check if user is already a captain of another clan
        $members_table = $wpdb->prefix . 'cod_clan_members';
        $existing_captain = $wpdb->get_var($wpdb->prepare(
            "SELECT clan_id FROM $members_table WHERE user_id = %d AND role = 'captain' AND status = 'active'",
            $data['captain_id']
        ));
        
        if ($existing_captain) {
            return new WP_Error('already_captain', __('User is already a captain of another clan.', 'cod-clan-league'));
        }
        
        // Insert clan
        $clan_data = array(
            'name' => sanitize_text_field($data['name']),
            'tag' => sanitize_text_field($data['tag']),
            'description' => sanitize_textarea_field($data['description'] ?? ''),
            'logo_url' => esc_url_raw($data['logo_url'] ?? ''),
            'captain_id' => intval($data['captain_id']),
            'status' => 'pending',
            'created_date' => current_time('mysql'),
        );
        
        $result = $wpdb->insert($clans_table, $clan_data);
        
        if ($result === false) {
            return new WP_Error('db_error', __('Failed to create clan.', 'cod-clan-league'));
        }
        
        $clan_id = $wpdb->insert_id;
        
        // Add captain as member
        $member_result = $this->add_member($clan_id, $data['captain_id'], 'captain', 'active');
        
        if (is_wp_error($member_result)) {
            // Rollback clan creation
            $wpdb->delete($clans_table, array('id' => $clan_id));
            return $member_result;
        }
        
        // Update user role to clan captain
        $user = get_user_by('id', $data['captain_id']);
        if ($user && !user_can($user, 'manage_cod_clans')) {
            $user->add_role('cod_clan_captain');
        }
        
        do_action('cod_clan_created', $clan_id, $data);
        
        return $clan_id;
    }
    
    /**
     * Add member to clan
     */
    public function add_member($clan_id, $user_id, $role = 'member', $status = 'pending') {
        global $wpdb;
        
        $members_table = $wpdb->prefix . 'cod_clan_members';
        
        // Check if user is already a member
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $members_table WHERE clan_id = %d AND user_id = %d",
            $clan_id,
            $user_id
        ));
        
        if ($existing) {
            return new WP_Error('already_member', __('User is already a member of this clan.', 'cod-clan-league'));
        }
        
        // Check clan size limits
        $options = get_option('cod_clan_league_options', array());
        $max_size = $options['max_clan_size'] ?? 10;
        
        $current_size = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $members_table WHERE clan_id = %d AND status = 'active'",
            $clan_id
        ));
        
        if ($current_size >= $max_size) {
            return new WP_Error('clan_full', __('Clan has reached maximum size.', 'cod-clan-league'));
        }
        
        $member_data = array(
            'clan_id' => $clan_id,
            'user_id' => $user_id,
            'role' => $role,
            'status' => $status,
            'joined_date' => current_time('mysql'),
        );
        
        $result = $wpdb->insert($members_table, $member_data);
        
        if ($result === false) {
            return new WP_Error('db_error', __('Failed to add member to clan.', 'cod-clan-league'));
        }
        
        // Update user role if needed
        $user = get_user_by('id', $user_id);
        if ($user && $status === 'active' && !user_can($user, 'manage_cod_clans')) {
            if ($role === 'captain') {
                $user->add_role('cod_clan_captain');
            } else {
                $user->add_role('cod_clan_member');
            }
        }
        
        do_action('cod_clan_member_added', $clan_id, $user_id, $role, $status);
        
        return $wpdb->insert_id;
    }
    
    /**
     * Send clan invitation
     */
    public function send_invitation($clan_id, $user_id, $invited_by) {
        global $wpdb;
        
        // Validate permissions
        if (!current_user_can('invite_clan_members') && !current_user_can('manage_cod_clans')) {
            return new WP_Error('no_permission', __('You do not have permission to invite members.', 'cod-clan-league'));
        }
        
        // Check if user exists
        $user = get_user_by('id', $user_id);
        if (!$user) {
            return new WP_Error('user_not_found', __('User not found.', 'cod-clan-league'));
        }
        
        // Check if user is already a member or has pending invitation
        $members_table = $wpdb->prefix . 'cod_clan_members';
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT status FROM $members_table WHERE clan_id = %d AND user_id = %d",
            $clan_id,
            $user_id
        ));
        
        if ($existing) {
            if ($existing === 'active') {
                return new WP_Error('already_member', __('User is already a member of this clan.', 'cod-clan-league'));
            } elseif ($existing === 'pending') {
                return new WP_Error('invitation_pending', __('User already has a pending invitation.', 'cod-clan-league'));
            }
        }
        
        // Check for existing invitation
        $invitations_table = $wpdb->prefix . 'cod_clan_invitations';
        $existing_invitation = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $invitations_table 
             WHERE clan_id = %d AND invited_user_id = %d AND status = 'pending' AND expires_date > NOW()",
            $clan_id,
            $user_id
        ));
        
        if ($existing_invitation) {
            return new WP_Error('invitation_exists', __('User already has a pending invitation.', 'cod-clan-league'));
        }
        
        // Create invitation
        $invitation_code = wp_generate_password(32, false);
        $expires_date = date('Y-m-d H:i:s', strtotime('+7 days'));
        
        $invitation_data = array(
            'clan_id' => $clan_id,
            'invited_user_id' => $user_id,
            'invited_by' => $invited_by,
            'invitation_code' => $invitation_code,
            'expires_date' => $expires_date,
            'created_date' => current_time('mysql'),
        );
        
        $result = $wpdb->insert($invitations_table, $invitation_data);
        
        if ($result === false) {
            return new WP_Error('db_error', __('Failed to create invitation.', 'cod-clan-league'));
        }
        
        // Send email notification
        $this->send_invitation_email($clan_id, $user_id, $invitation_code);
        
        do_action('cod_clan_invitation_sent', $clan_id, $user_id, $invitation_code);
        
        return $wpdb->insert_id;
    }
    
    /**
     * Respond to clan invitation
     */
    public function respond_to_invitation($invitation_code, $response) {
        global $wpdb;
        
        $invitations_table = $wpdb->prefix . 'cod_clan_invitations';
        
        // Get invitation
        $invitation = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $invitations_table 
             WHERE invitation_code = %s AND status = 'pending' AND expires_date > NOW()",
            $invitation_code
        ));
        
        if (!$invitation) {
            return new WP_Error('invalid_invitation', __('Invalid or expired invitation.', 'cod-clan-league'));
        }
        
        // Validate response
        if (!in_array($response, array('accepted', 'declined'))) {
            return new WP_Error('invalid_response', __('Invalid response.', 'cod-clan-league'));
        }
        
        // Update invitation status
        $wpdb->update(
            $invitations_table,
            array(
                'status' => $response,
                'responded_date' => current_time('mysql'),
            ),
            array('id' => $invitation->id)
        );
        
        if ($response === 'accepted') {
            // Add user to clan
            $result = $this->add_member($invitation->clan_id, $invitation->invited_user_id, 'member', 'active');
            
            if (is_wp_error($result)) {
                // Rollback invitation status
                $wpdb->update(
                    $invitations_table,
                    array('status' => 'pending'),
                    array('id' => $invitation->id)
                );
                return $result;
            }
        }
        
        do_action('cod_clan_invitation_responded', $invitation->clan_id, $invitation->invited_user_id, $response);
        
        return true;
    }
    
    /**
     * Remove member from clan
     */
    public function remove_member($clan_id, $user_id, $removed_by = null) {
        global $wpdb;
        
        $members_table = $wpdb->prefix . 'cod_clan_members';
        
        // Get member info
        $member = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $members_table WHERE clan_id = %d AND user_id = %d AND status = 'active'",
            $clan_id,
            $user_id
        ));
        
        if (!$member) {
            return new WP_Error('member_not_found', __('Member not found in clan.', 'cod-clan-league'));
        }
        
        // Cannot remove captain unless transferring captaincy
        if ($member->role === 'captain') {
            $member_count = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $members_table WHERE clan_id = %d AND status = 'active'",
                $clan_id
            ));
            
            if ($member_count > 1) {
                return new WP_Error('captain_transfer_required', __('Captain must transfer leadership before leaving.', 'cod-clan-league'));
            }
        }
        
        // Remove member
        $result = $wpdb->delete($members_table, array(
            'clan_id' => $clan_id,
            'user_id' => $user_id,
        ));
        
        if ($result === false) {
            return new WP_Error('db_error', __('Failed to remove member.', 'cod-clan-league'));
        }
        
        // Update user role
        $user = get_user_by('id', $user_id);
        if ($user) {
            $user->remove_role('cod_clan_captain');
            $user->remove_role('cod_clan_member');
        }
        
        // If this was the last member, deactivate clan
        $remaining_members = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $members_table WHERE clan_id = %d AND status = 'active'",
            $clan_id
        ));
        
        if ($remaining_members === 0) {
            $clans_table = $wpdb->prefix . 'cod_clans';
            $wpdb->update(
                $clans_table,
                array('status' => 'inactive'),
                array('id' => $clan_id)
            );
        }
        
        do_action('cod_clan_member_removed', $clan_id, $user_id, $removed_by);
        
        return true;
    }
    
    /**
     * Transfer clan captaincy
     */
    public function transfer_captaincy($clan_id, $new_captain_id, $current_captain_id) {
        global $wpdb;
        
        $members_table = $wpdb->prefix . 'cod_clan_members';
        
        // Validate current captain
        $current_captain = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $members_table 
             WHERE clan_id = %d AND user_id = %d AND role = 'captain' AND status = 'active'",
            $clan_id,
            $current_captain_id
        ));
        
        if (!$current_captain) {
            return new WP_Error('not_captain', __('User is not the current captain.', 'cod-clan-league'));
        }
        
        // Validate new captain is a member
        $new_captain = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $members_table 
             WHERE clan_id = %d AND user_id = %d AND status = 'active'",
            $clan_id,
            $new_captain_id
        ));
        
        if (!$new_captain) {
            return new WP_Error('not_member', __('New captain must be an active member of the clan.', 'cod-clan-league'));
        }
        
        // Update roles
        $wpdb->update(
            $members_table,
            array('role' => 'member'),
            array('clan_id' => $clan_id, 'user_id' => $current_captain_id)
        );
        
        $wpdb->update(
            $members_table,
            array('role' => 'captain'),
            array('clan_id' => $clan_id, 'user_id' => $new_captain_id)
        );
        
        // Update clan captain
        $clans_table = $wpdb->prefix . 'cod_clans';
        $wpdb->update(
            $clans_table,
            array('captain_id' => $new_captain_id),
            array('id' => $clan_id)
        );
        
        // Update user roles
        $old_captain = get_user_by('id', $current_captain_id);
        if ($old_captain) {
            $old_captain->remove_role('cod_clan_captain');
            $old_captain->add_role('cod_clan_member');
        }
        
        $new_captain_user = get_user_by('id', $new_captain_id);
        if ($new_captain_user) {
            $new_captain_user->remove_role('cod_clan_member');
            $new_captain_user->add_role('cod_clan_captain');
        }
        
        do_action('cod_clan_captaincy_transferred', $clan_id, $current_captain_id, $new_captain_id);
        
        return true;
    }
    
    /**
     * Get clan by ID
     */
    public function get_clan($clan_id) {
        global $wpdb;
        
        $clans_table = $wpdb->prefix . 'cod_clans';
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $clans_table WHERE id = %d",
            $clan_id
        ));
    }
    
    /**
     * Get clan members
     */
    public function get_clan_members($clan_id, $status = 'active') {
        global $wpdb;
        
        $members_table = $wpdb->prefix . 'cod_clan_members';
        
        $query = "SELECT cm.*, u.display_name, u.user_email 
                  FROM $members_table cm
                  INNER JOIN {$wpdb->users} u ON cm.user_id = u.ID
                  WHERE cm.clan_id = %d";
        
        $params = array($clan_id);
        
        if ($status) {
            $query .= " AND cm.status = %s";
            $params[] = $status;
        }
        
        $query .= " ORDER BY cm.role DESC, cm.joined_date ASC";
        
        return $wpdb->get_results($wpdb->prepare($query, $params));
    }
    
    /**
     * Send invitation email
     */
    private function send_invitation_email($clan_id, $user_id, $invitation_code) {
        $clan = $this->get_clan($clan_id);
        $user = get_user_by('id', $user_id);
        
        if (!$clan || !$user) {
            return false;
        }
        
        $invitation_url = add_query_arg(array(
            'cod_invitation' => $invitation_code,
            'action' => 'view',
        ), home_url());
        
        $subject = sprintf(__('Invitation to join %s clan', 'cod-clan-league'), $clan->name);
        
        $message = sprintf(
            __("You have been invited to join the %s clan.\n\nTo accept or decline this invitation, please visit:\n%s\n\nThis invitation will expire in 7 days.", 'cod-clan-league'),
            $clan->name,
            $invitation_url
        );
        
        return wp_mail($user->user_email, $subject, $message);
    }
    
    /**
     * Handle invitation response from URL
     */
    public function handle_invitation_response() {
        if (isset($_GET['cod_invitation']) && isset($_GET['action'])) {
            $invitation_code = sanitize_text_field($_GET['cod_invitation']);
            $action = sanitize_text_field($_GET['action']);
            
            if ($action === 'view') {
                // Redirect to invitation page or show invitation details
                // This would typically redirect to a frontend page
                return;
            }
            
            if (in_array($action, array('accept', 'decline')) && is_user_logged_in()) {
                $response = $action === 'accept' ? 'accepted' : 'declined';
                $result = $this->respond_to_invitation($invitation_code, $response);
                
                if (is_wp_error($result)) {
                    wp_die($result->get_error_message());
                } else {
                    $message = $response === 'accepted' 
                        ? __('Invitation accepted successfully!', 'cod-clan-league')
                        : __('Invitation declined.', 'cod-clan-league');
                    
                    wp_redirect(add_query_arg('message', urlencode($message), home_url()));
                    exit;
                }
            }
        }
    }
    
    /**
     * AJAX: Create clan
     */
    public function ajax_create_clan() {
        check_ajax_referer('cod_clan_league_nonce', 'nonce');
        
        if (!is_user_logged_in()) {
            wp_send_json_error(__('You must be logged in to create a clan.', 'cod-clan-league'));
        }
        
        $data = array(
            'name' => sanitize_text_field($_POST['name'] ?? ''),
            'tag' => sanitize_text_field($_POST['tag'] ?? ''),
            'description' => sanitize_textarea_field($_POST['description'] ?? ''),
            'logo_url' => esc_url_raw($_POST['logo_url'] ?? ''),
            'captain_id' => get_current_user_id(),
        );
        
        $result = $this->create_clan($data);
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(array(
                'clan_id' => $result,
                'message' => __('Clan created successfully!', 'cod-clan-league'),
            ));
        }
    }
    
    /**
     * AJAX: Invite member
     */
    public function ajax_invite_member() {
        check_ajax_referer('cod_clan_league_nonce', 'nonce');
        
        if (!is_user_logged_in()) {
            wp_send_json_error(__('You must be logged in.', 'cod-clan-league'));
        }
        
        $clan_id = intval($_POST['clan_id'] ?? 0);
        $user_id = intval($_POST['user_id'] ?? 0);
        
        $result = $this->send_invitation($clan_id, $user_id, get_current_user_id());
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(__('Invitation sent successfully!', 'cod-clan-league'));
        }
    }
    
    /**
     * AJAX: Respond to invitation
     */
    public function ajax_respond_invitation() {
        check_ajax_referer('cod_clan_league_nonce', 'nonce');
        
        if (!is_user_logged_in()) {
            wp_send_json_error(__('You must be logged in.', 'cod-clan-league'));
        }
        
        $invitation_code = sanitize_text_field($_POST['invitation_code'] ?? '');
        $response = sanitize_text_field($_POST['response'] ?? '');
        
        $result = $this->respond_to_invitation($invitation_code, $response);
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            $message = $response === 'accepted' 
                ? __('Invitation accepted!', 'cod-clan-league')
                : __('Invitation declined.', 'cod-clan-league');
            wp_send_json_success($message);
        }
    }
    
    /**
     * AJAX: Remove member
     */
    public function ajax_remove_member() {
        check_ajax_referer('cod_clan_league_nonce', 'nonce');
        
        if (!is_user_logged_in()) {
            wp_send_json_error(__('You must be logged in.', 'cod-clan-league'));
        }
        
        $clan_id = intval($_POST['clan_id'] ?? 0);
        $user_id = intval($_POST['user_id'] ?? 0);
        
        $result = $this->remove_member($clan_id, $user_id, get_current_user_id());
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(__('Member removed successfully!', 'cod-clan-league'));
        }
    }
    
    /**
     * AJAX: Leave clan
     */
    public function ajax_leave_clan() {
        check_ajax_referer('cod_clan_league_nonce', 'nonce');

        if (!is_user_logged_in()) {
            wp_send_json_error(__('You must be logged in.', 'cod-clan-league'));
        }

        $clan_id = intval($_POST['clan_id'] ?? 0);
        $user_id = get_current_user_id();

        $result = $this->remove_member($clan_id, $user_id, $user_id);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(__('You have left the clan.', 'cod-clan-league'));
        }
    }

    /**
     * Get clans list
     */
    public function get_clans($args = array()) {
        global $wpdb;

        $defaults = array(
            'status' => 'active',
            'limit' => -1,
            'offset' => 0,
            'orderby' => 'name',
            'order' => 'ASC',
        );

        $args = wp_parse_args($args, $defaults);

        $clans_table = $wpdb->prefix . 'cod_clans';
        $query = "SELECT * FROM $clans_table WHERE 1=1";
        $params = array();

        if ($args['status']) {
            $query .= " AND status = %s";
            $params[] = $args['status'];
        }

        $query .= " ORDER BY {$args['orderby']} {$args['order']}";

        if ($args['limit'] > 0) {
            $query .= " LIMIT %d";
            $params[] = $args['limit'];

            if ($args['offset'] > 0) {
                $query .= " OFFSET %d";
                $params[] = $args['offset'];
            }
        }

        if (!empty($params)) {
            return $wpdb->get_results($wpdb->prepare($query, $params));
        } else {
            return $wpdb->get_results($query);
        }
    }
}
