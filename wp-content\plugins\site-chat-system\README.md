# Site Chat System WordPress Plugin

A real-time chat system for WordPress that provides a floating chat widget for registered users. The chat appears in the bottom right corner (configurable) and allows users to communicate in real-time across your website.

## Features

### 🎯 **Core Chat Functionality**
- **Real-time messaging** with AJAX-powered updates
- **Floating chat widget** that appears on all pages
- **Registered users only** - secure and controlled environment
- **Multiple positioning options** (bottom-right, bottom-left, top-right, top-left)
- **Responsive design** that works on desktop and mobile
- **Auto-refresh** with configurable intervals

### 💬 **Advanced Chat Features**
- **Message reactions** with emoji support
- **Message editing and deletion** for users' own messages
- **Online user status** with real-time indicators
- **Typing indicators** to show when users are typing
- **Message history** with configurable limits
- **Emoji picker** with popular emojis
- **User avatars** from WordPress Gravatar
- **Notification badges** for unread messages

### 👥 **User Management**
- **Online users sidebar** showing who's currently active
- **User status indicators** (Online, Away, Busy, Offline)
- **User blocking functionality** (admin feature)
- **Role-based permissions** for moderation
- **Automatic offline detection** after inactivity

### 🎨 **Customization Options**
- **Multiple themes** (Default, Dark, Light)
- **Configurable positioning** of chat widget
- **Customizable refresh intervals**
- **Message length limits**
- **Auto-delete old messages** option
- **Sound and desktop notifications** support

### 🛡️ **Security & Moderation**
- **Input validation and sanitization**
- **WordPress nonce verification**
- **Rate limiting** to prevent spam
- **Admin moderation tools**
- **Message deletion capabilities**
- **User banning system**

### 📊 **Admin Dashboard**
- **Real-time statistics** (messages, online users, etc.)
- **Recent message monitoring**
- **User activity tracking**
- **Bulk message management**
- **Chat history export**
- **System health monitoring**

## Installation

1. Upload the plugin files to `/wp-content/plugins/site-chat-system/`
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Configure settings in **Site Chat > Settings**
4. The chat widget will automatically appear for logged-in users

## Configuration

### Basic Settings

Access **Site Chat > Settings** to configure:

- **Enable/Disable Chat**: Turn the chat system on or off
- **Max Message Length**: Set character limit for messages (50-2000)
- **Message History Limit**: Number of messages to load (10-200)
- **Refresh Interval**: How often to check for new messages (1-10 seconds)

### Appearance Settings

- **Chat Position**: Choose where the chat widget appears
- **Chat Theme**: Select from Default, Dark, or Light themes
- **Custom styling**: Override CSS for advanced customization

### Moderation Settings

- **Auto-delete Messages**: Automatically remove old messages
- **Delete After Days**: How long to keep messages (1-365 days)
- **User Permissions**: Control who can send messages
- **Banned Words Filter**: Block inappropriate content

## Usage

### For Users

1. **Opening Chat**: Click the chat icon in the corner of any page
2. **Sending Messages**: Type in the input field and press Enter or click Send
3. **Reactions**: Hover over messages and click the reaction button
4. **Emojis**: Click the emoji button to open the emoji picker
5. **Online Users**: Click the users button to see who's online
6. **Editing**: Click edit on your own messages to modify them
7. **Deleting**: Click delete to remove your own messages

### For Administrators

1. **Dashboard**: View chat statistics and recent activity
2. **Message Management**: Monitor and moderate chat messages
3. **User Management**: Ban users or manage permissions
4. **Settings**: Configure all chat system options
5. **Export Data**: Download chat history for backup

## Database Structure

The plugin creates these tables:

- `wp_chat_rooms` - Chat room information
- `wp_chat_messages` - All chat messages
- `wp_chat_room_participants` - User room memberships
- `wp_chat_online_users` - Online status tracking
- `wp_chat_message_reactions` - Message reactions
- `wp_chat_blocked_users` - User blocking data

## Hooks and Filters

### Actions

- `site_chat_message_sent` - Fired when a message is sent
- `site_chat_message_deleted` - Fired when a message is deleted
- `site_chat_user_online` - Fired when a user comes online
- `site_chat_user_offline` - Fired when a user goes offline

### Filters

- `site_chat_message_content` - Filter message content before saving
- `site_chat_user_permissions` - Filter user chat permissions
- `site_chat_widget_position` - Filter chat widget positioning
- `site_chat_refresh_interval` - Filter refresh timing

## Customization

### CSS Customization

Override styles by adding CSS to your theme:

```css
/* Change chat widget colors */
.site-chat-toggle {
    background: #your-color !important;
}

/* Customize message bubbles */
.site-chat-message-text {
    background: #your-color !important;
}
```

### JavaScript Hooks

Extend functionality with JavaScript:

```javascript
// Listen for new messages
$(document).on('site_chat_new_message', function(event, message) {
    // Your custom code here
});

// Customize message display
$(document).on('site_chat_before_display_message', function(event, message) {
    // Modify message before display
});
```

## Performance Considerations

- **AJAX Polling**: Uses efficient polling with configurable intervals
- **Message Limits**: Automatically limits message history to prevent bloat
- **Cleanup Tasks**: Scheduled cleanup of old data and offline users
- **Caching**: Optimized database queries with proper indexing
- **Rate Limiting**: Prevents spam and abuse

## Browser Support

- **Modern Browsers**: Chrome, Firefox, Safari, Edge (latest versions)
- **Mobile Support**: iOS Safari, Chrome Mobile, Samsung Internet
- **JavaScript Required**: Chat requires JavaScript to be enabled
- **WebSocket Ready**: Prepared for future WebSocket implementation

## Troubleshooting

### Common Issues

1. **Chat not appearing**: Check if user is logged in and plugin is activated
2. **Messages not updating**: Verify AJAX URL and nonce are correct
3. **Permission errors**: Check user roles and capabilities
4. **Database errors**: Ensure tables were created properly

### Debug Mode

Enable WordPress debug mode to see detailed error messages:

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Requirements

- **WordPress**: 5.0 or higher
- **PHP**: 7.4 or higher
- **MySQL**: 5.6 or higher
- **JavaScript**: Enabled in browser
- **User Registration**: Users must be logged in

## Security

- All inputs are sanitized and validated
- WordPress nonces prevent CSRF attacks
- Rate limiting prevents spam
- User permissions are properly checked
- SQL injection protection with prepared statements

## Support

For support and feature requests:

1. Check the plugin settings and documentation
2. Review the troubleshooting section
3. Contact the plugin developer
4. Submit issues through appropriate channels

## License

This plugin is licensed under the GPL v2 or later.

## Changelog

### Version 1.0.0
- Initial release
- Real-time chat functionality
- User management and moderation
- Admin dashboard and settings
- Multiple themes and positioning options
- Security and performance optimizations

## Credits

Developed with WordPress best practices and modern web standards for seamless chat communication.
