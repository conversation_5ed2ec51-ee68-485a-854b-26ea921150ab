<?php
/**
 * Database management class
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class COD_Clan_League_Database {
    
    /**
     * Database version
     */
    const DB_VERSION = '1.0.0';
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('plugins_loaded', array($this, 'check_database_version'));
    }
    
    /**
     * Check if database needs updating
     */
    public function check_database_version() {
        $installed_version = get_option('cod_clan_league_db_version', '0.0.0');
        
        if (version_compare($installed_version, self::DB_VERSION, '<')) {
            $this->create_tables();
            update_option('cod_clan_league_db_version', self::DB_VERSION);
        }
    }
    
    /**
     * Create database tables
     */
    public function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Clans table
        $clans_table = $wpdb->prefix . 'cod_clans';
        $clans_sql = "CREATE TABLE $clans_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            name varchar(100) NOT NULL,
            tag varchar(10) NOT NULL,
            description text,
            logo_url varchar(255),
            captain_id bigint(20) unsigned NOT NULL,
            status enum('active', 'inactive', 'pending') DEFAULT 'pending',
            created_date datetime DEFAULT CURRENT_TIMESTAMP,
            updated_date datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY unique_name (name),
            UNIQUE KEY unique_tag (tag),
            KEY captain_id (captain_id),
            KEY status (status)
        ) $charset_collate;";
        
        // Clan members table
        $clan_members_table = $wpdb->prefix . 'cod_clan_members';
        $clan_members_sql = "CREATE TABLE $clan_members_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            clan_id bigint(20) unsigned NOT NULL,
            user_id bigint(20) unsigned NOT NULL,
            role enum('captain', 'member') DEFAULT 'member',
            status enum('active', 'pending', 'declined') DEFAULT 'pending',
            joined_date datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY unique_clan_user (clan_id, user_id),
            KEY clan_id (clan_id),
            KEY user_id (user_id),
            KEY status (status)
        ) $charset_collate;";
        
        // Seasons table
        $seasons_table = $wpdb->prefix . 'cod_seasons';
        $seasons_sql = "CREATE TABLE $seasons_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            name varchar(100) NOT NULL,
            description text,
            start_date date NOT NULL,
            end_date date NOT NULL,
            status enum('upcoming', 'active', 'completed') DEFAULT 'upcoming',
            max_teams int(11) DEFAULT 16,
            created_date datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY status (status),
            KEY dates (start_date, end_date)
        ) $charset_collate;";
        
        // Season teams table
        $season_teams_table = $wpdb->prefix . 'cod_season_teams';
        $season_teams_sql = "CREATE TABLE $season_teams_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            season_id bigint(20) unsigned NOT NULL,
            clan_id bigint(20) unsigned NOT NULL,
            status enum('approved', 'pending', 'declined') DEFAULT 'pending',
            points int(11) DEFAULT 0,
            matches_played int(11) DEFAULT 0,
            matches_won int(11) DEFAULT 0,
            matches_lost int(11) DEFAULT 0,
            joined_date datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY unique_season_clan (season_id, clan_id),
            KEY season_id (season_id),
            KEY clan_id (clan_id),
            KEY status (status),
            KEY points (points)
        ) $charset_collate;";
        
        // Matches table
        $matches_table = $wpdb->prefix . 'cod_matches';
        $matches_sql = "CREATE TABLE $matches_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            season_id bigint(20) unsigned NOT NULL,
            home_team_id bigint(20) unsigned NOT NULL,
            away_team_id bigint(20) unsigned NOT NULL,
            scheduled_date datetime,
            home_score int(11),
            away_score int(11),
            winner_team_id bigint(20) unsigned,
            status enum('scheduled', 'pending_approval', 'completed', 'cancelled') DEFAULT 'scheduled',
            screenshot_url varchar(255),
            notes text,
            reported_by bigint(20) unsigned,
            approved_by bigint(20) unsigned,
            created_date datetime DEFAULT CURRENT_TIMESTAMP,
            completed_date datetime,
            PRIMARY KEY (id),
            KEY season_id (season_id),
            KEY home_team_id (home_team_id),
            KEY away_team_id (away_team_id),
            KEY status (status),
            KEY scheduled_date (scheduled_date)
        ) $charset_collate;";
        
        // Match events table (for detailed match tracking)
        $match_events_table = $wpdb->prefix . 'cod_match_events';
        $match_events_sql = "CREATE TABLE $match_events_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            match_id bigint(20) unsigned NOT NULL,
            event_type enum('goal', 'penalty', 'card', 'substitution', 'other') NOT NULL,
            team_id bigint(20) unsigned NOT NULL,
            player_id bigint(20) unsigned,
            event_time int(11),
            description text,
            created_date datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY match_id (match_id),
            KEY team_id (team_id),
            KEY event_type (event_type)
        ) $charset_collate;";
        
        // Invitations table
        $invitations_table = $wpdb->prefix . 'cod_clan_invitations';
        $invitations_sql = "CREATE TABLE $invitations_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            clan_id bigint(20) unsigned NOT NULL,
            invited_user_id bigint(20) unsigned NOT NULL,
            invited_by bigint(20) unsigned NOT NULL,
            status enum('pending', 'accepted', 'declined', 'expired') DEFAULT 'pending',
            invitation_code varchar(32) NOT NULL,
            expires_date datetime NOT NULL,
            created_date datetime DEFAULT CURRENT_TIMESTAMP,
            responded_date datetime,
            PRIMARY KEY (id),
            UNIQUE KEY unique_invitation_code (invitation_code),
            KEY clan_id (clan_id),
            KEY invited_user_id (invited_user_id),
            KEY status (status),
            KEY expires_date (expires_date)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        
        dbDelta($clans_sql);
        dbDelta($clan_members_sql);
        dbDelta($seasons_sql);
        dbDelta($season_teams_sql);
        dbDelta($matches_sql);
        dbDelta($match_events_sql);
        dbDelta($invitations_sql);
    }
    
    /**
     * Drop all plugin tables
     */
    public function drop_tables() {
        global $wpdb;
        
        $tables = array(
            $wpdb->prefix . 'cod_match_events',
            $wpdb->prefix . 'cod_matches',
            $wpdb->prefix . 'cod_season_teams',
            $wpdb->prefix . 'cod_seasons',
            $wpdb->prefix . 'cod_clan_invitations',
            $wpdb->prefix . 'cod_clan_members',
            $wpdb->prefix . 'cod_clans'
        );
        
        foreach ($tables as $table) {
            $wpdb->query("DROP TABLE IF EXISTS $table");
        }
        
        delete_option('cod_clan_league_db_version');
    }
    
    /**
     * Get table name with prefix
     */
    public function get_table_name($table) {
        global $wpdb;
        return $wpdb->prefix . 'cod_' . $table;
    }
}
