/**
 * COD Clan League Admin JavaScript
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        CODClanLeagueAdmin.init();
    });

    // Main admin object
    window.CODClanLeagueAdmin = {
        
        // Initialize all functionality
        init: function() {
            this.bindEvents();
            this.initComponents();
        },

        // Bind event handlers
        bindEvents: function() {
            // Match approval buttons
            $(document).on('click', '.cod-approve-match', this.handleMatchApproval);
            
            // Fixture generation
            $(document).on('click', '.cod-generate-fixtures', this.handleFixtureGeneration);
            
            // League table updates
            $(document).on('click', '.cod-update-table', this.handleTableUpdate);
            
            // Team approval
            $(document).on('click', '.cod-approve-team', this.handleTeamApproval);
            
            // Bulk actions
            $(document).on('click', '.cod-bulk-action', this.handleBulkAction);
            
            // Form submissions
            $(document).on('submit', '.cod-admin-form', this.handleFormSubmission);
            
            // Delete confirmations
            $(document).on('click', '[data-confirm-delete]', this.handleDeleteConfirmation);
            
            // Tab switching
            $(document).on('click', '.cod-admin-tab', this.handleTabSwitch);
        },

        // Initialize components
        initComponents: function() {
            this.initDataTables();
            this.initDatePickers();
            this.initSelectBoxes();
            this.initCharts();
            this.initTooltips();
        },

        // Handle match approval
        handleMatchApproval: function(e) {
            e.preventDefault();
            
            var $btn = $(this);
            var matchId = $btn.data('match-id');
            var approved = $btn.data('approved');
            var originalText = $btn.text();
            
            $btn.prop('disabled', true).text('Processing...');
            
            $.ajax({
                url: codClanLeagueAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'cod_approve_match_result',
                    nonce: codClanLeagueAdmin.nonce,
                    match_id: matchId,
                    approved: approved
                },
                success: function(response) {
                    if (response.success) {
                        if (approved) {
                            $btn.closest('tr').fadeOut(function() {
                                $(this).remove();
                            });
                        } else {
                            $btn.closest('tr').addClass('rejected');
                        }
                        CODClanLeagueAdmin.showNotification(response.data, 'success');
                    } else {
                        CODClanLeagueAdmin.showNotification(response.data, 'error');
                        $btn.prop('disabled', false).text(originalText);
                    }
                },
                error: function() {
                    CODClanLeagueAdmin.showNotification(codClanLeagueAdmin.strings.error, 'error');
                    $btn.prop('disabled', false).text(originalText);
                }
            });
        },

        // Handle fixture generation
        handleFixtureGeneration: function(e) {
            e.preventDefault();
            
            var $btn = $(this);
            var seasonId = $btn.data('season-id');
            var confirmMessage = 'Generate fixtures for this season? This cannot be undone.';
            
            if (!confirm(confirmMessage)) {
                return;
            }
            
            $btn.prop('disabled', true).html('<span class="cod-spinner"></span> Generating...');
            
            $.ajax({
                url: codClanLeagueAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'cod_generate_fixtures',
                    nonce: codClanLeagueAdmin.nonce,
                    season_id: seasonId
                },
                success: function(response) {
                    if (response.success) {
                        CODClanLeagueAdmin.showNotification(response.data, 'success');
                        $btn.text('Fixtures Generated').addClass('button-disabled');
                    } else {
                        CODClanLeagueAdmin.showNotification(response.data, 'error');
                        $btn.prop('disabled', false).text('Generate Fixtures');
                    }
                },
                error: function() {
                    CODClanLeagueAdmin.showNotification(codClanLeagueAdmin.strings.error, 'error');
                    $btn.prop('disabled', false).text('Generate Fixtures');
                }
            });
        },

        // Handle league table updates
        handleTableUpdate: function(e) {
            e.preventDefault();
            
            var $btn = $(this);
            var seasonId = $btn.data('season-id');
            
            $btn.prop('disabled', true).html('<span class="cod-spinner"></span> Updating...');
            
            $.ajax({
                url: codClanLeagueAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'cod_update_league_table',
                    nonce: codClanLeagueAdmin.nonce,
                    season_id: seasonId
                },
                success: function(response) {
                    if (response.success) {
                        CODClanLeagueAdmin.showNotification(response.data, 'success');
                        // Refresh the page to show updated table
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    } else {
                        CODClanLeagueAdmin.showNotification(response.data, 'error');
                    }
                },
                error: function() {
                    CODClanLeagueAdmin.showNotification(codClanLeagueAdmin.strings.error, 'error');
                },
                complete: function() {
                    $btn.prop('disabled', false).text('Update Table');
                }
            });
        },

        // Handle team approval
        handleTeamApproval: function(e) {
            e.preventDefault();
            
            var $btn = $(this);
            var teamId = $btn.data('team-id');
            var approved = $btn.data('approved');
            
            $btn.prop('disabled', true);
            
            $.ajax({
                url: codClanLeagueAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'cod_approve_team',
                    nonce: codClanLeagueAdmin.nonce,
                    team_id: teamId,
                    approved: approved
                },
                success: function(response) {
                    if (response.success) {
                        var $row = $btn.closest('tr');
                        var $statusCell = $row.find('.team-status');
                        
                        if (approved) {
                            $statusCell.html('<span class="cod-status cod-status-approved">Approved</span>');
                            $btn.text('Approved').addClass('button-disabled');
                        } else {
                            $statusCell.html('<span class="cod-status cod-status-declined">Declined</span>');
                            $btn.text('Declined').addClass('button-disabled');
                        }
                        
                        CODClanLeagueAdmin.showNotification(response.data, 'success');
                    } else {
                        CODClanLeagueAdmin.showNotification(response.data, 'error');
                        $btn.prop('disabled', false);
                    }
                },
                error: function() {
                    CODClanLeagueAdmin.showNotification(codClanLeagueAdmin.strings.error, 'error');
                    $btn.prop('disabled', false);
                }
            });
        },

        // Handle bulk actions
        handleBulkAction: function(e) {
            e.preventDefault();
            
            var $btn = $(this);
            var action = $btn.data('action');
            var $checkboxes = $('.cod-bulk-checkbox:checked');
            
            if ($checkboxes.length === 0) {
                alert('Please select items to perform bulk action.');
                return;
            }
            
            var confirmMessage = 'Are you sure you want to ' + action + ' ' + $checkboxes.length + ' item(s)?';
            if (!confirm(confirmMessage)) {
                return;
            }
            
            var ids = [];
            $checkboxes.each(function() {
                ids.push($(this).val());
            });
            
            $btn.prop('disabled', true);
            
            $.ajax({
                url: codClanLeagueAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'cod_bulk_action',
                    nonce: codClanLeagueAdmin.nonce,
                    bulk_action: action,
                    ids: ids
                },
                success: function(response) {
                    if (response.success) {
                        CODClanLeagueAdmin.showNotification(response.data, 'success');
                        location.reload();
                    } else {
                        CODClanLeagueAdmin.showNotification(response.data, 'error');
                    }
                },
                error: function() {
                    CODClanLeagueAdmin.showNotification(codClanLeagueAdmin.strings.error, 'error');
                },
                complete: function() {
                    $btn.prop('disabled', false);
                }
            });
        },

        // Handle form submissions
        handleFormSubmission: function(e) {
            var $form = $(this);
            var $submitBtn = $form.find('button[type="submit"]');
            var originalText = $submitBtn.text();
            
            $submitBtn.prop('disabled', true).text('Saving...');
            
            // Re-enable button after a delay if no AJAX is handling it
            setTimeout(function() {
                if ($submitBtn.prop('disabled')) {
                    $submitBtn.prop('disabled', false).text(originalText);
                }
            }, 5000);
        },

        // Handle delete confirmations
        handleDeleteConfirmation: function(e) {
            var message = $(this).data('confirm-delete') || 'Are you sure you want to delete this item?';
            if (!confirm(message)) {
                e.preventDefault();
                return false;
            }
        },

        // Handle tab switching
        handleTabSwitch: function(e) {
            e.preventDefault();
            
            var $tab = $(this);
            var target = $tab.data('tab');
            var $container = $tab.closest('.cod-admin-tabs');
            
            // Update active tab
            $container.find('.cod-admin-tab').removeClass('nav-tab-active');
            $tab.addClass('nav-tab-active');
            
            // Update active content
            $container.find('.cod-tab-content').removeClass('active');
            $('#' + target).addClass('active');
        },

        // Initialize data tables
        initDataTables: function() {
            if ($.fn.DataTable) {
                $('.cod-admin-table').DataTable({
                    responsive: true,
                    pageLength: 25,
                    order: [[1, 'asc']],
                    columnDefs: [
                        { orderable: false, targets: [0, -1] } // Disable sorting on checkbox and actions columns
                    ],
                    language: {
                        search: 'Search:',
                        lengthMenu: 'Show _MENU_ entries per page',
                        info: 'Showing _START_ to _END_ of _TOTAL_ entries',
                        paginate: {
                            first: 'First',
                            last: 'Last',
                            next: 'Next',
                            previous: 'Previous'
                        }
                    }
                });
            }
        },

        // Initialize date pickers
        initDatePickers: function() {
            if ($.fn.datepicker) {
                $('.cod-datepicker').datepicker({
                    dateFormat: 'yy-mm-dd',
                    changeMonth: true,
                    changeYear: true
                });
            }
        },

        // Initialize select boxes
        initSelectBoxes: function() {
            if ($.fn.select2) {
                $('.cod-select2').select2({
                    width: '100%',
                    placeholder: 'Select an option...'
                });
            }
        },

        // Initialize charts
        initCharts: function() {
            // Initialize Chart.js charts if present
            if (typeof Chart !== 'undefined') {
                $('.cod-chart').each(function() {
                    var $canvas = $(this);
                    var chartType = $canvas.data('chart-type') || 'line';
                    var chartData = $canvas.data('chart-data') || {};
                    
                    new Chart($canvas[0], {
                        type: chartType,
                        data: chartData,
                        options: {
                            responsive: true,
                            maintainAspectRatio: false
                        }
                    });
                });
            }
        },

        // Initialize tooltips
        initTooltips: function() {
            $('[data-tooltip]').each(function() {
                $(this).attr('title', $(this).data('tooltip'));
            });
        },

        // Show notification
        showNotification: function(message, type) {
            type = type || 'info';
            
            var $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
            
            $('.wrap h1').after($notice);
            
            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $notice.fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
        },

        // Show loading spinner
        showLoading: function($element) {
            $element.append('<span class="cod-spinner"></span>');
        },

        // Hide loading spinner
        hideLoading: function($element) {
            $element.find('.cod-spinner').remove();
        },

        // Utility: Format number
        formatNumber: function(num, decimals) {
            decimals = decimals || 0;
            return parseFloat(num).toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        },

        // Utility: Validate email
        validateEmail: function(email) {
            var re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        },

        // Utility: Debounce function
        debounce: function(func, wait, immediate) {
            var timeout;
            return function() {
                var context = this, args = arguments;
                var later = function() {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                var callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };
        }
    };

    // Global functions for inline use
    window.codApproveMatch = function(matchId, approved) {
        CODClanLeagueAdmin.handleMatchApproval.call($('.cod-approve-match[data-match-id="' + matchId + '"][data-approved="' + approved + '"]')[0]);
    };

    window.codGenerateFixtures = function(seasonId) {
        CODClanLeagueAdmin.handleFixtureGeneration.call($('.cod-generate-fixtures[data-season-id="' + seasonId + '"]')[0]);
    };

    window.codUpdateTable = function(seasonId) {
        CODClanLeagueAdmin.handleTableUpdate.call($('.cod-update-table[data-season-id="' + seasonId + '"]')[0]);
    };

})(jQuery);
