<?php
/**
 * Admin panel and backend functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class COD_Clan_League_Admin {
    
    /**
     * Initialize admin
     */
    public function init() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        
        // Add custom columns to post type lists
        add_filter('manage_cod_clan_posts_columns', array($this, 'clan_columns'));
        add_action('manage_cod_clan_posts_custom_column', array($this, 'clan_column_content'), 10, 2);
        add_filter('manage_cod_season_posts_columns', array($this, 'season_columns'));
        add_action('manage_cod_season_posts_custom_column', array($this, 'season_column_content'), 10, 2);
        add_filter('manage_cod_match_posts_columns', array($this, 'match_columns'));
        add_action('manage_cod_match_posts_custom_column', array($this, 'match_column_content'), 10, 2);
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Main menu
        add_menu_page(
            __('COD Clan League', 'cod-clan-league'),
            __('COD League', 'cod-clan-league'),
            'view_cod_admin_panel',
            'cod-clan-league',
            array($this, 'admin_dashboard'),
            'dashicons-groups',
            30
        );
        
        // Dashboard
        add_submenu_page(
            'cod-clan-league',
            __('Dashboard', 'cod-clan-league'),
            __('Dashboard', 'cod-clan-league'),
            'view_cod_admin_panel',
            'cod-clan-league',
            array($this, 'admin_dashboard')
        );
        
        // Seasons
        add_submenu_page(
            'cod-clan-league',
            __('Seasons', 'cod-clan-league'),
            __('Seasons', 'cod-clan-league'),
            'manage_cod_seasons',
            'cod-seasons',
            array($this, 'admin_seasons')
        );
        
        // Teams
        add_submenu_page(
            'cod-clan-league',
            __('Teams', 'cod-clan-league'),
            __('Teams', 'cod-clan-league'),
            'manage_cod_teams',
            'cod-teams',
            array($this, 'admin_teams')
        );
        
        // Matches
        add_submenu_page(
            'cod-clan-league',
            __('Matches', 'cod-clan-league'),
            __('Matches', 'cod-clan-league'),
            'manage_cod_matches',
            'cod-matches',
            array($this, 'admin_matches')
        );
        
        // Settings
        add_submenu_page(
            'cod-clan-league',
            __('Settings', 'cod-clan-league'),
            __('Settings', 'cod-clan-league'),
            'manage_cod_leagues',
            'cod-settings',
            array($this, 'admin_settings')
        );
    }
    
    /**
     * Admin init
     */
    public function admin_init() {
        // Admin initialization - can be used for additional setup if needed
    }
    
    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'cod-clan-league') === false && strpos($hook, 'cod-') === false) {
            return;
        }
        
        wp_enqueue_style(
            'cod-clan-league-admin',
            COD_CLAN_LEAGUE_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            COD_CLAN_LEAGUE_VERSION
        );
        
        wp_enqueue_script(
            'cod-clan-league-admin',
            COD_CLAN_LEAGUE_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery', 'wp-util'),
            COD_CLAN_LEAGUE_VERSION,
            true
        );
        
        wp_localize_script('cod-clan-league-admin', 'codClanLeagueAdmin', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('cod_clan_league_admin_nonce'),
            'strings' => array(
                'confirmDelete' => __('Are you sure you want to delete this?', 'cod-clan-league'),
                'error' => __('An error occurred. Please try again.', 'cod-clan-league'),
            )
        ));
    }
    
    /**
     * Admin dashboard
     */
    public function admin_dashboard() {
        global $wpdb;
        
        // Get statistics
        $clans_table = $wpdb->prefix . 'cod_clans';
        $seasons_table = $wpdb->prefix . 'cod_seasons';
        $matches_table = $wpdb->prefix . 'cod_matches';
        $members_table = $wpdb->prefix . 'cod_clan_members';
        
        $stats = array(
            'total_clans' => $wpdb->get_var("SELECT COUNT(*) FROM $clans_table WHERE status = 'active'"),
            'total_seasons' => $wpdb->get_var("SELECT COUNT(*) FROM $seasons_table"),
            'active_seasons' => $wpdb->get_var("SELECT COUNT(*) FROM $seasons_table WHERE status = 'active'"),
            'total_matches' => $wpdb->get_var("SELECT COUNT(*) FROM $matches_table"),
            'completed_matches' => $wpdb->get_var("SELECT COUNT(*) FROM $matches_table WHERE status = 'completed'"),
            'pending_matches' => $wpdb->get_var("SELECT COUNT(*) FROM $matches_table WHERE status = 'pending_approval'"),
            'total_members' => $wpdb->get_var("SELECT COUNT(*) FROM $members_table WHERE status = 'active'"),
        );
        
        // Get recent activity
        $recent_matches = $wpdb->get_results(
            "SELECT m.*, 
                    ht.clan_id as home_clan_id, hc.name as home_clan_name,
                    at.clan_id as away_clan_id, ac.name as away_clan_name
             FROM $matches_table m
             INNER JOIN {$wpdb->prefix}cod_season_teams ht ON m.home_team_id = ht.id
             INNER JOIN {$wpdb->prefix}cod_season_teams at ON m.away_team_id = at.id
             INNER JOIN $clans_table hc ON ht.clan_id = hc.id
             INNER JOIN $clans_table ac ON at.clan_id = ac.id
             WHERE m.status = 'pending_approval'
             ORDER BY m.completed_date DESC
             LIMIT 5"
        );
        
        ?>
        <div class="wrap">
            <h1><?php _e('COD Clan League Dashboard', 'cod-clan-league'); ?></h1>
            
            <div class="cod-dashboard-stats">
                <div class="cod-stat-box">
                    <h3><?php echo $stats['total_clans']; ?></h3>
                    <p><?php _e('Active Clans', 'cod-clan-league'); ?></p>
                </div>
                <div class="cod-stat-box">
                    <h3><?php echo $stats['active_seasons']; ?></h3>
                    <p><?php _e('Active Seasons', 'cod-clan-league'); ?></p>
                </div>
                <div class="cod-stat-box">
                    <h3><?php echo $stats['completed_matches']; ?></h3>
                    <p><?php _e('Completed Matches', 'cod-clan-league'); ?></p>
                </div>
                <div class="cod-stat-box">
                    <h3><?php echo $stats['pending_matches']; ?></h3>
                    <p><?php _e('Pending Approval', 'cod-clan-league'); ?></p>
                </div>
                <div class="cod-stat-box">
                    <h3><?php echo $stats['total_members']; ?></h3>
                    <p><?php _e('Total Members', 'cod-clan-league'); ?></p>
                </div>
            </div>
            
            <?php if (!empty($recent_matches)): ?>
            <div class="cod-recent-activity">
                <h2><?php _e('Matches Pending Approval', 'cod-clan-league'); ?></h2>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('Match', 'cod-clan-league'); ?></th>
                            <th><?php _e('Score', 'cod-clan-league'); ?></th>
                            <th><?php _e('Reported', 'cod-clan-league'); ?></th>
                            <th><?php _e('Actions', 'cod-clan-league'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_matches as $match): ?>
                        <tr>
                            <td><?php echo esc_html($match->home_clan_name . ' vs ' . $match->away_clan_name); ?></td>
                            <td><?php echo intval($match->home_score) . ' - ' . intval($match->away_score); ?></td>
                            <td><?php echo date_i18n(get_option('date_format'), strtotime($match->completed_date)); ?></td>
                            <td>
                                <button class="button button-primary cod-approve-match" data-match-id="<?php echo $match->id; ?>" data-approved="true">
                                    <?php _e('Approve', 'cod-clan-league'); ?>
                                </button>
                                <button class="button cod-approve-match" data-match-id="<?php echo $match->id; ?>" data-approved="false">
                                    <?php _e('Reject', 'cod-clan-league'); ?>
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
            
            <div class="cod-quick-actions">
                <h2><?php _e('Quick Actions', 'cod-clan-league'); ?></h2>
                <p>
                    <a href="<?php echo admin_url('admin.php?page=cod-seasons'); ?>" class="button button-primary">
                        <?php _e('Manage Seasons', 'cod-clan-league'); ?>
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=cod-teams'); ?>" class="button">
                        <?php _e('Manage Teams', 'cod-clan-league'); ?>
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=cod-matches'); ?>" class="button">
                        <?php _e('Manage Matches', 'cod-clan-league'); ?>
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=cod-settings'); ?>" class="button">
                        <?php _e('Settings', 'cod-clan-league'); ?>
                    </a>
                </p>
            </div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            $('.cod-approve-match').on('click', function() {
                var matchId = $(this).data('match-id');
                var approved = $(this).data('approved');
                var button = $(this);
                
                $.ajax({
                    url: codClanLeagueAdmin.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'cod_approve_match_result',
                        nonce: codClanLeagueAdmin.nonce,
                        match_id: matchId,
                        approved: approved
                    },
                    success: function(response) {
                        if (response.success) {
                            button.closest('tr').fadeOut();
                        } else {
                            alert(response.data);
                        }
                    },
                    error: function() {
                        alert(codClanLeagueAdmin.strings.error);
                    }
                });
            });
        });
        </script>
        <?php
    }
    
    /**
     * Admin seasons page
     */
    public function admin_seasons() {
        $league_manager = new COD_Clan_League_League_Manager();
        
        // Handle form submissions
        if (isset($_POST['create_season']) && wp_verify_nonce($_POST['_wpnonce'], 'cod_create_season')) {
            $data = array(
                'name' => sanitize_text_field($_POST['season_name']),
                'description' => sanitize_textarea_field($_POST['season_description']),
                'start_date' => sanitize_text_field($_POST['start_date']),
                'end_date' => sanitize_text_field($_POST['end_date']),
                'max_teams' => intval($_POST['max_teams']),
            );
            
            $result = $league_manager->create_season($data);
            
            if (is_wp_error($result)) {
                echo '<div class="notice notice-error"><p>' . $result->get_error_message() . '</p></div>';
            } else {
                echo '<div class="notice notice-success"><p>' . __('Season created successfully!', 'cod-clan-league') . '</p></div>';
            }
        }
        
        // Get seasons
        global $wpdb;
        $seasons_table = $wpdb->prefix . 'cod_seasons';
        $seasons = $wpdb->get_results("SELECT * FROM $seasons_table ORDER BY created_date DESC");
        
        ?>
        <div class="wrap">
            <h1><?php _e('Manage Seasons', 'cod-clan-league'); ?></h1>
            
            <div class="cod-admin-section">
                <h2><?php _e('Create New Season', 'cod-clan-league'); ?></h2>
                <form method="post" action="">
                    <?php wp_nonce_field('cod_create_season'); ?>
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Season Name', 'cod-clan-league'); ?></th>
                            <td><input type="text" name="season_name" class="regular-text" required></td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Description', 'cod-clan-league'); ?></th>
                            <td><textarea name="season_description" rows="3" class="large-text"></textarea></td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Start Date', 'cod-clan-league'); ?></th>
                            <td><input type="date" name="start_date" required></td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('End Date', 'cod-clan-league'); ?></th>
                            <td><input type="date" name="end_date" required></td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Maximum Teams', 'cod-clan-league'); ?></th>
                            <td><input type="number" name="max_teams" value="16" min="2" max="64"></td>
                        </tr>
                    </table>
                    <?php submit_button(__('Create Season', 'cod-clan-league'), 'primary', 'create_season'); ?>
                </form>
            </div>
            
            <div class="cod-admin-section">
                <h2><?php _e('Existing Seasons', 'cod-clan-league'); ?></h2>
                <?php if ($seasons): ?>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('Name', 'cod-clan-league'); ?></th>
                            <th><?php _e('Status', 'cod-clan-league'); ?></th>
                            <th><?php _e('Dates', 'cod-clan-league'); ?></th>
                            <th><?php _e('Teams', 'cod-clan-league'); ?></th>
                            <th><?php _e('Actions', 'cod-clan-league'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($seasons as $season): ?>
                        <?php
                        $team_count = $wpdb->get_var($wpdb->prepare(
                            "SELECT COUNT(*) FROM {$wpdb->prefix}cod_season_teams WHERE season_id = %d AND status = 'approved'",
                            $season->id
                        ));
                        ?>
                        <tr>
                            <td><strong><?php echo esc_html($season->name); ?></strong></td>
                            <td><span class="cod-status cod-status-<?php echo esc_attr($season->status); ?>"><?php echo esc_html(ucfirst($season->status)); ?></span></td>
                            <td><?php echo date_i18n(get_option('date_format'), strtotime($season->start_date)) . ' - ' . date_i18n(get_option('date_format'), strtotime($season->end_date)); ?></td>
                            <td><?php echo $team_count . '/' . $season->max_teams; ?></td>
                            <td>
                                <button class="button cod-generate-fixtures" data-season-id="<?php echo $season->id; ?>">
                                    <?php _e('Generate Fixtures', 'cod-clan-league'); ?>
                                </button>
                                <button class="button cod-update-table" data-season-id="<?php echo $season->id; ?>">
                                    <?php _e('Update Table', 'cod-clan-league'); ?>
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <?php else: ?>
                <p><?php _e('No seasons found.', 'cod-clan-league'); ?></p>
                <?php endif; ?>
            </div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            $('.cod-generate-fixtures').on('click', function() {
                var seasonId = $(this).data('season-id');
                var button = $(this);
                
                if (!confirm('<?php _e('Generate fixtures for this season? This cannot be undone.', 'cod-clan-league'); ?>')) {
                    return;
                }
                
                button.prop('disabled', true);
                
                $.ajax({
                    url: codClanLeagueAdmin.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'cod_generate_fixtures',
                        nonce: codClanLeagueAdmin.nonce,
                        season_id: seasonId
                    },
                    success: function(response) {
                        if (response.success) {
                            alert(response.data);
                        } else {
                            alert(response.data);
                        }
                        button.prop('disabled', false);
                    },
                    error: function() {
                        alert(codClanLeagueAdmin.strings.error);
                        button.prop('disabled', false);
                    }
                });
            });
            
            $('.cod-update-table').on('click', function() {
                var seasonId = $(this).data('season-id');
                var button = $(this);
                
                button.prop('disabled', true);
                
                $.ajax({
                    url: codClanLeagueAdmin.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'cod_update_league_table',
                        nonce: codClanLeagueAdmin.nonce,
                        season_id: seasonId
                    },
                    success: function(response) {
                        if (response.success) {
                            alert(response.data);
                        } else {
                            alert(response.data);
                        }
                        button.prop('disabled', false);
                    },
                    error: function() {
                        alert(codClanLeagueAdmin.strings.error);
                        button.prop('disabled', false);
                    }
                });
            });
        });
        </script>
        <?php
    }

    /**
     * Admin teams page
     */
    public function admin_teams() {
        global $wpdb;

        // Handle team approval
        if (isset($_POST['approve_team']) && wp_verify_nonce($_POST['_wpnonce'], 'cod_approve_team')) {
            $team_id = intval($_POST['team_id']);
            $approved = $_POST['action'] === 'approve';

            $league_manager = new COD_Clan_League_League_Manager();
            $result = $league_manager->approve_team($team_id, $approved);

            if (is_wp_error($result)) {
                echo '<div class="notice notice-error"><p>' . $result->get_error_message() . '</p></div>';
            } else {
                $message = $approved ? __('Team approved successfully!', 'cod-clan-league') : __('Team declined.', 'cod-clan-league');
                echo '<div class="notice notice-success"><p>' . $message . '</p></div>';
            }
        }

        // Get teams data
        $season_teams_table = $wpdb->prefix . 'cod_season_teams';
        $clans_table = $wpdb->prefix . 'cod_clans';
        $seasons_table = $wpdb->prefix . 'cod_seasons';

        $teams = $wpdb->get_results(
            "SELECT st.*, c.name as clan_name, c.tag as clan_tag, s.name as season_name
             FROM $season_teams_table st
             INNER JOIN $clans_table c ON st.clan_id = c.id
             INNER JOIN $seasons_table s ON st.season_id = s.id
             ORDER BY st.created_date DESC"
        );

        ?>
        <div class="wrap">
            <h1><?php _e('Manage Teams', 'cod-clan-league'); ?></h1>

            <?php if (!empty($teams)): ?>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('Clan', 'cod-clan-league'); ?></th>
                        <th><?php _e('Season', 'cod-clan-league'); ?></th>
                        <th><?php _e('Status', 'cod-clan-league'); ?></th>
                        <th><?php _e('Registered', 'cod-clan-league'); ?></th>
                        <th><?php _e('Stats', 'cod-clan-league'); ?></th>
                        <th><?php _e('Actions', 'cod-clan-league'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($teams as $team): ?>
                    <tr>
                        <td>
                            <strong><?php echo esc_html($team->clan_name); ?></strong>
                            <?php if ($team->clan_tag): ?>
                            <span class="cod-clan-tag">[<?php echo esc_html($team->clan_tag); ?>]</span>
                            <?php endif; ?>
                        </td>
                        <td><?php echo esc_html($team->season_name); ?></td>
                        <td class="team-status">
                            <span class="cod-status cod-status-<?php echo esc_attr($team->status); ?>">
                                <?php echo esc_html(ucfirst($team->status)); ?>
                            </span>
                        </td>
                        <td><?php echo date_i18n(get_option('date_format'), strtotime($team->created_date)); ?></td>
                        <td>
                            <?php printf(__('P: %d, W: %d, L: %d, Pts: %d', 'cod-clan-league'),
                                intval($team->matches_played),
                                intval($team->matches_won),
                                intval($team->matches_lost),
                                intval($team->points)
                            ); ?>
                        </td>
                        <td>
                            <?php if ($team->status === 'pending'): ?>
                            <button class="button button-primary cod-approve-team" data-team-id="<?php echo $team->id; ?>" data-approved="true">
                                <?php _e('Approve', 'cod-clan-league'); ?>
                            </button>
                            <button class="button cod-approve-team" data-team-id="<?php echo $team->id; ?>" data-approved="false">
                                <?php _e('Decline', 'cod-clan-league'); ?>
                            </button>
                            <?php else: ?>
                            <span class="description"><?php _e('No actions available', 'cod-clan-league'); ?></span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php else: ?>
            <p><?php _e('No teams found.', 'cod-clan-league'); ?></p>
            <?php endif; ?>
        </div>

        <script>
        jQuery(document).ready(function($) {
            $('.cod-approve-team').on('click', function() {
                var teamId = $(this).data('team-id');
                var approved = $(this).data('approved');
                var button = $(this);

                button.prop('disabled', true);

                $.ajax({
                    url: codClanLeagueAdmin.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'cod_approve_team',
                        nonce: codClanLeagueAdmin.nonce,
                        team_id: teamId,
                        approved: approved
                    },
                    success: function(response) {
                        if (response.success) {
                            var $row = button.closest('tr');
                            var $statusCell = $row.find('.team-status');

                            if (approved) {
                                $statusCell.html('<span class="cod-status cod-status-approved">Approved</span>');
                                button.text('Approved').addClass('button-disabled');
                            } else {
                                $statusCell.html('<span class="cod-status cod-status-declined">Declined</span>');
                                button.text('Declined').addClass('button-disabled');
                            }

                            // Hide other action buttons
                            $row.find('.cod-approve-team').not(button).hide();
                        } else {
                            alert(response.data);
                            button.prop('disabled', false);
                        }
                    },
                    error: function() {
                        alert(codClanLeagueAdmin.strings.error);
                        button.prop('disabled', false);
                    }
                });
            });
        });
        </script>
        <?php
    }

    /**
     * Admin matches page
     */
    public function admin_matches() {
        global $wpdb;

        // Handle match approval
        if (isset($_POST['approve_match']) && wp_verify_nonce($_POST['_wpnonce'], 'cod_approve_match')) {
            $match_id = intval($_POST['match_id']);
            $approved = $_POST['action'] === 'approve';

            $match_manager = new COD_Clan_League_Match_Manager();
            $result = $match_manager->approve_match_result($match_id, $approved);

            if (is_wp_error($result)) {
                echo '<div class="notice notice-error"><p>' . $result->get_error_message() . '</p></div>';
            } else {
                $message = $approved ? __('Match result approved!', 'cod-clan-league') : __('Match result rejected.', 'cod-clan-league');
                echo '<div class="notice notice-success"><p>' . $message . '</p></div>';
            }
        }

        // Get matches data
        $matches_table = $wpdb->prefix . 'cod_matches';
        $season_teams_table = $wpdb->prefix . 'cod_season_teams';
        $clans_table = $wpdb->prefix . 'cod_clans';
        $seasons_table = $wpdb->prefix . 'cod_seasons';

        $matches = $wpdb->get_results(
            "SELECT m.*,
                    s.name as season_name,
                    hc.name as home_clan_name, hc.tag as home_clan_tag,
                    ac.name as away_clan_name, ac.tag as away_clan_tag
             FROM $matches_table m
             INNER JOIN $seasons_table s ON m.season_id = s.id
             INNER JOIN $season_teams_table ht ON m.home_team_id = ht.id
             INNER JOIN $season_teams_table at ON m.away_team_id = at.id
             INNER JOIN $clans_table hc ON ht.clan_id = hc.id
             INNER JOIN $clans_table ac ON at.clan_id = ac.id
             ORDER BY m.scheduled_date DESC, m.created_date DESC
             LIMIT 50"
        );

        ?>
        <div class="wrap">
            <h1><?php _e('Manage Matches', 'cod-clan-league'); ?></h1>

            <?php if (!empty($matches)): ?>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('Match', 'cod-clan-league'); ?></th>
                        <th><?php _e('Season', 'cod-clan-league'); ?></th>
                        <th><?php _e('Score', 'cod-clan-league'); ?></th>
                        <th><?php _e('Status', 'cod-clan-league'); ?></th>
                        <th><?php _e('Scheduled', 'cod-clan-league'); ?></th>
                        <th><?php _e('Actions', 'cod-clan-league'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($matches as $match): ?>
                    <tr>
                        <td>
                            <strong><?php echo esc_html($match->home_clan_name); ?></strong>
                            <?php if ($match->home_clan_tag): ?>
                            <span class="cod-clan-tag">[<?php echo esc_html($match->home_clan_tag); ?>]</span>
                            <?php endif; ?>
                            vs
                            <strong><?php echo esc_html($match->away_clan_name); ?></strong>
                            <?php if ($match->away_clan_tag): ?>
                            <span class="cod-clan-tag">[<?php echo esc_html($match->away_clan_tag); ?>]</span>
                            <?php endif; ?>
                        </td>
                        <td><?php echo esc_html($match->season_name); ?></td>
                        <td>
                            <?php if ($match->home_score !== null && $match->away_score !== null): ?>
                            <strong><?php echo intval($match->home_score) . ' - ' . intval($match->away_score); ?></strong>
                            <?php else: ?>
                            <span class="description">-</span>
                            <?php endif; ?>
                        </td>
                        <td class="match-status">
                            <span class="cod-status cod-status-<?php echo esc_attr($match->status); ?>">
                                <?php echo esc_html(ucfirst(str_replace('_', ' ', $match->status))); ?>
                            </span>
                        </td>
                        <td>
                            <?php if ($match->scheduled_date): ?>
                            <?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($match->scheduled_date)); ?>
                            <?php else: ?>
                            <span class="description"><?php _e('Not scheduled', 'cod-clan-league'); ?></span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($match->status === 'pending_approval'): ?>
                            <button class="button button-primary cod-approve-match" data-match-id="<?php echo $match->id; ?>" data-approved="true">
                                <?php _e('Approve', 'cod-clan-league'); ?>
                            </button>
                            <button class="button cod-approve-match" data-match-id="<?php echo $match->id; ?>" data-approved="false">
                                <?php _e('Reject', 'cod-clan-league'); ?>
                            </button>
                            <?php elseif ($match->status === 'completed'): ?>
                            <button class="button cod-override-match" data-match-id="<?php echo $match->id; ?>">
                                <?php _e('Override', 'cod-clan-league'); ?>
                            </button>
                            <?php else: ?>
                            <span class="description"><?php _e('No actions available', 'cod-clan-league'); ?></span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php else: ?>
            <p><?php _e('No matches found.', 'cod-clan-league'); ?></p>
            <?php endif; ?>
        </div>

        <script>
        jQuery(document).ready(function($) {
            $('.cod-approve-match').on('click', function() {
                var matchId = $(this).data('match-id');
                var approved = $(this).data('approved');
                var button = $(this);

                button.prop('disabled', true);

                $.ajax({
                    url: codClanLeagueAdmin.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'cod_approve_match_result',
                        nonce: codClanLeagueAdmin.nonce,
                        match_id: matchId,
                        approved: approved
                    },
                    success: function(response) {
                        if (response.success) {
                            var $row = button.closest('tr');
                            var $statusCell = $row.find('.match-status');

                            if (approved) {
                                $statusCell.html('<span class="cod-status cod-status-completed">Completed</span>');
                                $row.find('.cod-approve-match').hide();
                                $row.find('td:last-child').append('<button class="button cod-override-match" data-match-id="' + matchId + '">Override</button>');
                            } else {
                                $statusCell.html('<span class="cod-status cod-status-scheduled">Scheduled</span>');
                                $row.find('.cod-approve-match').hide();
                            }
                        } else {
                            alert(response.data);
                            button.prop('disabled', false);
                        }
                    },
                    error: function() {
                        alert(codClanLeagueAdmin.strings.error);
                        button.prop('disabled', false);
                    }
                });
            });

            $(document).on('click', '.cod-override-match', function() {
                var matchId = $(this).data('match-id');

                if (confirm('<?php _e('Override this match result? This will reset it to scheduled status.', 'cod-clan-league'); ?>')) {
                    // Implementation for match override
                    alert('<?php _e('Match override functionality would be implemented here.', 'cod-clan-league'); ?>');
                }
            });
        });
        </script>
        <?php
    }

    /**
     * Admin settings page
     */
    public function admin_settings() {
        // Handle settings save
        if (isset($_POST['save_settings']) && wp_verify_nonce($_POST['_wpnonce'], 'cod_save_settings')) {
            $options = array(
                'points_for_win' => intval($_POST['points_for_win'] ?? 3),
                'points_for_loss' => intval($_POST['points_for_loss'] ?? 0),
                'allow_draws' => isset($_POST['allow_draws']),
                'require_match_screenshots' => isset($_POST['require_match_screenshots']),
                'auto_approve_results' => isset($_POST['auto_approve_results']),
                'max_clan_size' => intval($_POST['max_clan_size'] ?? 10),
                'min_clan_size' => intval($_POST['min_clan_size'] ?? 3),
            );

            update_option('cod_clan_league_options', $options);
            echo '<div class="notice notice-success"><p>' . __('Settings saved successfully!', 'cod-clan-league') . '</p></div>';
        }

        $options = get_option('cod_clan_league_options', array());
        ?>
        <div class="wrap">
            <h1><?php _e('COD Clan League Settings', 'cod-clan-league'); ?></h1>

            <form method="post" action="">
                <?php wp_nonce_field('cod_save_settings'); ?>

                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Points for Win', 'cod-clan-league'); ?></th>
                        <td>
                            <input type="number" name="points_for_win" value="<?php echo intval($options['points_for_win'] ?? 3); ?>" min="0" max="10" />
                            <p class="description"><?php _e('Points awarded for winning a match.', 'cod-clan-league'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Points for Loss', 'cod-clan-league'); ?></th>
                        <td>
                            <input type="number" name="points_for_loss" value="<?php echo intval($options['points_for_loss'] ?? 0); ?>" min="0" max="5" />
                            <p class="description"><?php _e('Points awarded for losing a match.', 'cod-clan-league'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Allow Draws', 'cod-clan-league'); ?></th>
                        <td>
                            <input type="checkbox" name="allow_draws" value="1" <?php checked(1, $options['allow_draws'] ?? false); ?> />
                            <label><?php _e('Allow matches to end in draws', 'cod-clan-league'); ?></label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Require Screenshots', 'cod-clan-league'); ?></th>
                        <td>
                            <input type="checkbox" name="require_match_screenshots" value="1" <?php checked(1, $options['require_match_screenshots'] ?? true); ?> />
                            <label><?php _e('Require screenshot uploads for match results', 'cod-clan-league'); ?></label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Auto-approve Results', 'cod-clan-league'); ?></th>
                        <td>
                            <input type="checkbox" name="auto_approve_results" value="1" <?php checked(1, $options['auto_approve_results'] ?? false); ?> />
                            <label><?php _e('Automatically approve match results without admin review', 'cod-clan-league'); ?></label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Maximum Clan Size', 'cod-clan-league'); ?></th>
                        <td>
                            <input type="number" name="max_clan_size" value="<?php echo intval($options['max_clan_size'] ?? 10); ?>" min="1" max="50" />
                            <p class="description"><?php _e('Maximum number of members allowed in a clan.', 'cod-clan-league'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Minimum Clan Size', 'cod-clan-league'); ?></th>
                        <td>
                            <input type="number" name="min_clan_size" value="<?php echo intval($options['min_clan_size'] ?? 3); ?>" min="1" max="20" />
                            <p class="description"><?php _e('Minimum number of members required to register for a season.', 'cod-clan-league'); ?></p>
                        </td>
                    </tr>
                </table>

                <?php submit_button(__('Save Settings', 'cod-clan-league'), 'primary', 'save_settings'); ?>
            </form>

            <div class="cod-admin-section">
                <h2><?php _e('System Information', 'cod-clan-league'); ?></h2>
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Plugin Version', 'cod-clan-league'); ?></th>
                        <td><?php echo COD_CLAN_LEAGUE_VERSION; ?></td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Database Version', 'cod-clan-league'); ?></th>
                        <td><?php echo get_option('cod_clan_league_db_version', '1.0.0'); ?></td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('WordPress Version', 'cod-clan-league'); ?></th>
                        <td><?php echo get_bloginfo('version'); ?></td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('PHP Version', 'cod-clan-league'); ?></th>
                        <td><?php echo PHP_VERSION; ?></td>
                    </tr>
                </table>
            </div>
        </div>
        <?php
    }



    /**
     * Custom columns for clan posts
     */
    public function clan_columns($columns) {
        $new_columns = array();
        $new_columns['cb'] = $columns['cb'];
        $new_columns['title'] = $columns['title'];
        $new_columns['clan_tag'] = __('Tag', 'cod-clan-league');
        $new_columns['clan_captain'] = __('Captain', 'cod-clan-league');
        $new_columns['clan_status'] = __('Status', 'cod-clan-league');
        $new_columns['clan_members'] = __('Members', 'cod-clan-league');
        $new_columns['date'] = $columns['date'];
        return $new_columns;
    }

    public function clan_column_content($column, $post_id) {
        switch ($column) {
            case 'clan_tag':
                echo esc_html(get_post_meta($post_id, '_clan_tag', true));
                break;
            case 'clan_captain':
                $captain_id = get_post_meta($post_id, '_clan_captain', true);
                if ($captain_id) {
                    $captain = get_user_by('id', $captain_id);
                    echo $captain ? esc_html($captain->display_name) : __('Unknown', 'cod-clan-league');
                }
                break;
            case 'clan_status':
                $status = get_post_meta($post_id, '_clan_status', true);
                echo '<span class="cod-status cod-status-' . esc_attr($status) . '">' . esc_html(ucfirst($status)) . '</span>';
                break;
            case 'clan_members':
                global $wpdb;
                $count = $wpdb->get_var($wpdb->prepare(
                    "SELECT COUNT(*) FROM {$wpdb->prefix}cod_clan_members cm
                     INNER JOIN {$wpdb->prefix}cod_clans c ON cm.clan_id = c.id
                     WHERE c.id = (SELECT id FROM {$wpdb->prefix}cod_clans WHERE name = %s LIMIT 1) AND cm.status = 'active'",
                    get_the_title($post_id)
                ));
                echo intval($count);
                break;
        }
    }

    /**
     * Custom columns for season posts
     */
    public function season_columns($columns) {
        $new_columns = array();
        $new_columns['cb'] = $columns['cb'];
        $new_columns['title'] = $columns['title'];
        $new_columns['season_dates'] = __('Dates', 'cod-clan-league');
        $new_columns['season_status'] = __('Status', 'cod-clan-league');
        $new_columns['season_teams'] = __('Teams', 'cod-clan-league');
        $new_columns['date'] = $columns['date'];
        return $new_columns;
    }

    public function season_column_content($column, $post_id) {
        switch ($column) {
            case 'season_dates':
                $start = get_post_meta($post_id, '_season_start_date', true);
                $end = get_post_meta($post_id, '_season_end_date', true);
                if ($start && $end) {
                    echo date_i18n(get_option('date_format'), strtotime($start)) . ' - ' . date_i18n(get_option('date_format'), strtotime($end));
                }
                break;
            case 'season_status':
                $status = get_post_meta($post_id, '_season_status', true);
                echo '<span class="cod-status cod-status-' . esc_attr($status) . '">' . esc_html(ucfirst($status)) . '</span>';
                break;
            case 'season_teams':
                global $wpdb;
                $count = $wpdb->get_var($wpdb->prepare(
                    "SELECT COUNT(*) FROM {$wpdb->prefix}cod_season_teams st
                     INNER JOIN {$wpdb->prefix}cod_seasons s ON st.season_id = s.id
                     WHERE s.name = %s AND st.status = 'approved'",
                    get_the_title($post_id)
                ));
                $max = get_post_meta($post_id, '_season_max_teams', true) ?: 16;
                echo intval($count) . '/' . intval($max);
                break;
        }
    }

    /**
     * Custom columns for match posts
     */
    public function match_columns($columns) {
        $new_columns = array();
        $new_columns['cb'] = $columns['cb'];
        $new_columns['title'] = $columns['title'];
        $new_columns['match_teams'] = __('Teams', 'cod-clan-league');
        $new_columns['match_score'] = __('Score', 'cod-clan-league');
        $new_columns['match_status'] = __('Status', 'cod-clan-league');
        $new_columns['match_date'] = __('Scheduled', 'cod-clan-league');
        $new_columns['date'] = $columns['date'];
        return $new_columns;
    }

    public function match_column_content($column, $post_id) {
        switch ($column) {
            case 'match_teams':
                // This would require getting team names from the database
                echo __('Team vs Team', 'cod-clan-league');
                break;
            case 'match_score':
                $home_score = get_post_meta($post_id, '_match_home_score', true);
                $away_score = get_post_meta($post_id, '_match_away_score', true);
                if ($home_score !== '' && $away_score !== '') {
                    echo intval($home_score) . ' - ' . intval($away_score);
                } else {
                    echo '-';
                }
                break;
            case 'match_status':
                $status = get_post_meta($post_id, '_match_status', true) ?: 'scheduled';
                echo '<span class="cod-status cod-status-' . esc_attr($status) . '">' . esc_html(ucfirst(str_replace('_', ' ', $status))) . '</span>';
                break;
            case 'match_date':
                $date = get_post_meta($post_id, '_match_scheduled_date', true);
                if ($date) {
                    echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($date));
                }
                break;
        }
    }
}
