=== Rookie ===
Contributors: <PERSON><PERSON><PERSON>, br<PERSON><PERSON><PERSON>, ayla<PERSON>, sav<PERSON><PERSON>, ka<PERSON><PERSON>, AndreKelling
Tags: black, blue, brown, gray, green, orange, pink, purple, red, silver, tan, white, yellow, dark, light, one-column, two-columns, right-sidebar, fluid-layout, responsive-layout, custom-background, custom-colors, custom-menu, editor-style, featured-images, full-width-template, rtl-language-support, sticky-post, threaded-comments, translation-ready
Requires at least: 3.8
Tested up to: 5.7
Stable tag: 1.5.4
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

== Description ==
Rookie is a fully responsive theme made for sports organizations looking to use the SportsPress plugin. Once you’ve installed the theme and SportsPress, you'll be able to select a preset for your sport and demo content to help you get started on building your sports website.

* Responsive Layout
* Custom Colors
* WooCommerce Support
* SportsPress Support
* The GPL v2.0 or later license. :) Use it to make something cool.

= Custom Templates =

Rookie provides compatibility with [WooCommerce](https://wordpress.org/plugins/woocommerce/) and [SportsPress](https://wordpress.org/plugins/sportspress/). The included templates are used for displaying content using these plugins:

1. sportspress.php - The default template for displaying SportsPress pages.
2. sportspress/single-event.php - A single SportsPress event page.
3. sportspress/single-tournament.php - A single SportsPress tournament page.
4. woocommerce/archive-product.php - An archive of WooCommerce products.

== Installation ==

1. In your admin panel, go to Appearance -> Themes and click the Add New button.
2. Click Upload and Choose File, then select the theme's ZIP file. Click Install Now.
3. Click Activate to use your new theme right away.

== Frequently Asked Questions ==

= How do I change the color scheme? =

You can change the colors of your site easily using Rookie.

1. In your admin panel, go to Appearance -> Customize.
2. Now you will see the Customizer and a tab called 'Colors'. Click this tab.
3. You can now select the colors for each area listed.
4. Once you are happy with your color changes you can click save and your changes will be reflected on your live site.

= Quick Specs =

1. The main content width is 620px or 66% of the content width minus 40px.
2. The left or right sidebar width is 300px or 34% of the content width minus 40px.
3. The double sidebar width is 200px on each side or 24% of the content width minus 40px.
4. Featured Images default to 620px wide by 413px high.
5. Header Images default to 1000px wide by 150px high.

= Additional Licenses =

Rookie is based on Underscores http://underscores.me/ - GPL v2.0
TGM Plugin Activation: Copyright (c) 2012, Thomas Griffin (thomasgriffinmedia.com) - GPL v2.0 or later
Timeago jQuery plugin: Copyright (c) 2008-2013, Ryan McGeary (<EMAIL>) - MIT License
Lato font: Copyright (c) 2010-2011, tyPoland Lukasz Dziedzic (<EMAIL>) - SIL Open Font License v1.1
Oswald font: Copyright (c) 2011-2012, Vernon Adams (<EMAIL>) - SIL Open Font License v1.1
All images: Copyright (c) 2011-2016, ThemeBoy (<EMAIL>) - GPL v2.0
