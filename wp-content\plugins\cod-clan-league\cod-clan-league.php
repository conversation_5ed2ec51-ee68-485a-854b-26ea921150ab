<?php
/**
 * Plugin Name: Call of Duty Clan League
 * Plugin URI: https://yoursite.com/cod-clan-league
 * Description: A comprehensive WordPress plugin for managing Call of Duty clan leagues with team registration, match reporting, and league management.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://yoursite.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: cod-clan-league
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.3
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('COD_CLAN_LEAGUE_VERSION', '1.0.0');
define('COD_CLAN_LEAGUE_PLUGIN_FILE', __FILE__);
define('COD_CLAN_LEAGUE_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('COD_CLAN_LEAGUE_PLUGIN_URL', plugin_dir_url(__FILE__));
define('COD_CLAN_LEAGUE_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main plugin class
 */
class COD_Clan_League {
    
    /**
     * Single instance of the plugin
     */
    private static $instance = null;
    
    /**
     * Plugin components
     */
    public $database;
    public $post_types;
    public $user_roles;
    public $clan_manager;
    public $league_manager;
    public $match_manager;
    public $frontend;
    public $admin;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->load_dependencies();
        $this->init_components();
    }
    
    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        register_uninstall_hook(__FILE__, array('COD_Clan_League', 'uninstall'));
        
        add_action('init', array($this, 'init'));
        add_action('plugins_loaded', array($this, 'load_textdomain'));
    }
    
    /**
     * Load plugin dependencies
     */
    private function load_dependencies() {
        // Core classes
        require_once COD_CLAN_LEAGUE_PLUGIN_DIR . 'includes/class-database.php';
        require_once COD_CLAN_LEAGUE_PLUGIN_DIR . 'includes/class-post-types.php';
        require_once COD_CLAN_LEAGUE_PLUGIN_DIR . 'includes/class-user-roles.php';
        
        // Manager classes
        require_once COD_CLAN_LEAGUE_PLUGIN_DIR . 'includes/class-clan-manager.php';
        require_once COD_CLAN_LEAGUE_PLUGIN_DIR . 'includes/class-league-manager.php';
        require_once COD_CLAN_LEAGUE_PLUGIN_DIR . 'includes/class-match-manager.php';
        
        // Frontend and admin
        require_once COD_CLAN_LEAGUE_PLUGIN_DIR . 'includes/class-frontend.php';
        require_once COD_CLAN_LEAGUE_PLUGIN_DIR . 'includes/class-admin.php';
        
        // Utilities
        require_once COD_CLAN_LEAGUE_PLUGIN_DIR . 'includes/class-security.php';
        require_once COD_CLAN_LEAGUE_PLUGIN_DIR . 'includes/class-utilities.php';
    }
    
    /**
     * Initialize plugin components
     */
    private function init_components() {
        $this->database = new COD_Clan_League_Database();
        $this->post_types = new COD_Clan_League_Post_Types();
        $this->user_roles = new COD_Clan_League_User_Roles();
        $this->clan_manager = new COD_Clan_League_Clan_Manager();
        $this->league_manager = new COD_Clan_League_League_Manager();
        $this->match_manager = new COD_Clan_League_Match_Manager();
        $this->frontend = new COD_Clan_League_Frontend();
        $this->admin = new COD_Clan_League_Admin();
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Initialize components that need to run on init
        $this->post_types->init();
        $this->user_roles->init();
        $this->frontend->init();
        $this->admin->init();
    }
    
    /**
     * Load plugin textdomain
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'cod-clan-league',
            false,
            dirname(COD_CLAN_LEAGUE_PLUGIN_BASENAME) . '/languages'
        );
    }
    
    /**
     * Enqueue frontend scripts and styles
     */
    public function enqueue_frontend_scripts() {
        wp_enqueue_style(
            'cod-clan-league-frontend',
            COD_CLAN_LEAGUE_PLUGIN_URL . 'assets/css/frontend.css',
            array(),
            COD_CLAN_LEAGUE_VERSION
        );
        
        wp_enqueue_script(
            'cod-clan-league-frontend',
            COD_CLAN_LEAGUE_PLUGIN_URL . 'assets/js/frontend.js',
            array('jquery'),
            COD_CLAN_LEAGUE_VERSION,
            true
        );
        
        wp_localize_script('cod-clan-league-frontend', 'codClanLeague', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('cod_clan_league_nonce'),
            'strings' => array(
                'confirmDelete' => __('Are you sure you want to delete this?', 'cod-clan-league'),
                'error' => __('An error occurred. Please try again.', 'cod-clan-league'),
            )
        ));
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        // Only load on plugin admin pages
        if (strpos($hook, 'cod-clan-league') === false) {
            return;
        }
        
        wp_enqueue_style(
            'cod-clan-league-admin',
            COD_CLAN_LEAGUE_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            COD_CLAN_LEAGUE_VERSION
        );
        
        wp_enqueue_script(
            'cod-clan-league-admin',
            COD_CLAN_LEAGUE_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery', 'wp-util'),
            COD_CLAN_LEAGUE_VERSION,
            true
        );
        
        wp_localize_script('cod-clan-league-admin', 'codClanLeagueAdmin', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('cod_clan_league_admin_nonce'),
            'strings' => array(
                'confirmDelete' => __('Are you sure you want to delete this?', 'cod-clan-league'),
                'error' => __('An error occurred. Please try again.', 'cod-clan-league'),
            )
        ));
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables
        $this->database->create_tables();
        
        // Add user roles and capabilities
        $this->user_roles->add_roles();
        
        // Flush rewrite rules
        flush_rewrite_rules();
        
        // Set default options
        $this->set_default_options();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin uninstall
     */
    public static function uninstall() {
        // Remove database tables
        $database = new COD_Clan_League_Database();
        $database->drop_tables();
        
        // Remove user roles
        $user_roles = new COD_Clan_League_User_Roles();
        $user_roles->remove_roles();
        
        // Remove options
        delete_option('cod_clan_league_options');
        delete_option('cod_clan_league_version');
    }
    
    /**
     * Set default plugin options
     */
    private function set_default_options() {
        $default_options = array(
            'points_for_win' => 3,
            'points_for_loss' => 0,
            'allow_draws' => false,
            'require_match_screenshots' => true,
            'auto_approve_results' => false,
            'max_clan_size' => 10,
            'min_clan_size' => 3,
        );
        
        add_option('cod_clan_league_options', $default_options);
        add_option('cod_clan_league_version', COD_CLAN_LEAGUE_VERSION);
    }
}

/**
 * Initialize the plugin
 */
function cod_clan_league() {
    return COD_Clan_League::get_instance();
}

// Start the plugin
cod_clan_league();
