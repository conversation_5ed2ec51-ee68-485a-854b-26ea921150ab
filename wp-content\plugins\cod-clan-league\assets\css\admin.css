/**
 * COD Clan League Admin Styles
 */

/* Dashboard Stats */
.cod-dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.cod-stat-box {
    background: #fff;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
    border-left: 4px solid #007cba;
}

.cod-stat-box h3 {
    font-size: 36px;
    font-weight: 700;
    margin: 0 0 10px;
    color: #007cba;
}

.cod-stat-box p {
    margin: 0;
    color: #666;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 0.5px;
}

/* Admin Sections */
.cod-admin-section {
    background: #fff;
    padding: 25px;
    margin: 20px 0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.cod-admin-section h2 {
    margin-top: 0;
    padding-bottom: 15px;
    border-bottom: 2px solid #007cba;
    color: #333;
}

/* Recent Activity */
.cod-recent-activity {
    background: #fff;
    padding: 25px;
    margin: 20px 0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.cod-recent-activity h2 {
    margin-top: 0;
    padding-bottom: 15px;
    border-bottom: 2px solid #007cba;
    color: #333;
}

/* Quick Actions */
.cod-quick-actions {
    background: #f8f9fa;
    padding: 25px;
    margin: 20px 0;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.cod-quick-actions h2 {
    margin-top: 0;
    color: #333;
}

.cod-quick-actions .button {
    margin-right: 10px;
    margin-bottom: 10px;
}

/* Status Indicators */
.cod-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.cod-status-active,
.cod-status-completed,
.cod-status-approved,
.cod-status-success {
    background: #d4edda;
    color: #155724;
}

.cod-status-pending,
.cod-status-upcoming,
.cod-status-warning {
    background: #fff3cd;
    color: #856404;
}

.cod-status-inactive,
.cod-status-cancelled,
.cod-status-declined,
.cod-status-danger {
    background: #f8d7da;
    color: #721c24;
}

.cod-status-pending-approval {
    background: #cce5ff;
    color: #004085;
}

.cod-status-scheduled {
    background: #e2e3e5;
    color: #383d41;
}

/* Form Enhancements */
.form-table th {
    width: 200px;
    font-weight: 600;
}

.form-table td input[type="text"],
.form-table td input[type="email"],
.form-table td input[type="url"],
.form-table td input[type="number"],
.form-table td input[type="date"],
.form-table td input[type="datetime-local"],
.form-table td textarea,
.form-table td select {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
    transition: border-color 0.3s ease;
}

.form-table td input:focus,
.form-table td textarea:focus,
.form-table td select:focus {
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.1);
    outline: none;
}

/* Custom Buttons */
.cod-approve-match,
.cod-generate-fixtures,
.cod-update-table {
    margin-right: 5px;
}

.cod-approve-match[data-approved="true"] {
    background: #28a745;
    border-color: #28a745;
    color: white;
}

.cod-approve-match[data-approved="true"]:hover {
    background: #218838;
    border-color: #1e7e34;
}

.cod-approve-match[data-approved="false"] {
    background: #dc3545;
    border-color: #dc3545;
    color: white;
}

.cod-approve-match[data-approved="false"]:hover {
    background: #c82333;
    border-color: #bd2130;
}

/* Loading States */
.button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Data Tables */
.wp-list-table .cod-status {
    white-space: nowrap;
}

.wp-list-table .column-clan_tag {
    width: 80px;
}

.wp-list-table .column-clan_status,
.wp-list-table .column-season_status,
.wp-list-table .column-match_status {
    width: 120px;
}

.wp-list-table .column-clan_members,
.wp-list-table .column-season_teams {
    width: 80px;
    text-align: center;
}

.wp-list-table .column-match_score {
    width: 100px;
    text-align: center;
    font-weight: 600;
}

/* Responsive Admin */
@media (max-width: 1200px) {
    .cod-dashboard-stats {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
    
    .cod-stat-box {
        padding: 20px;
    }
    
    .cod-stat-box h3 {
        font-size: 28px;
    }
}

@media (max-width: 768px) {
    .cod-dashboard-stats {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
    }
    
    .cod-stat-box {
        padding: 15px;
    }
    
    .cod-stat-box h3 {
        font-size: 24px;
    }
    
    .cod-admin-section,
    .cod-recent-activity,
    .cod-quick-actions {
        padding: 20px;
    }
    
    .form-table th {
        width: auto;
        display: block;
        padding-bottom: 5px;
    }
    
    .form-table td {
        display: block;
        padding-top: 0;
    }
}

/* Notifications */
.cod-admin-notice {
    background: #fff;
    border-left: 4px solid #007cba;
    padding: 12px;
    margin: 15px 0;
    box-shadow: 0 1px 1px rgba(0,0,0,0.04);
}

.cod-admin-notice.notice-success {
    border-left-color: #46b450;
}

.cod-admin-notice.notice-error {
    border-left-color: #dc3232;
}

.cod-admin-notice.notice-warning {
    border-left-color: #ffb900;
}

/* Settings Page */
.cod-settings-section {
    background: #fff;
    padding: 25px;
    margin: 20px 0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.cod-settings-section h3 {
    margin-top: 0;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

/* Help Text */
.cod-help-text {
    color: #666;
    font-style: italic;
    font-size: 13px;
    margin-top: 5px;
}

/* Action Buttons */
.cod-action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.cod-action-buttons .button {
    margin: 0;
}

/* Progress Indicators */
.cod-progress {
    background: #f0f0f0;
    border-radius: 10px;
    height: 20px;
    overflow: hidden;
    margin: 10px 0;
}

.cod-progress-bar {
    background: #007cba;
    height: 100%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

/* Tooltips */
.cod-tooltip {
    position: relative;
    cursor: help;
}

.cod-tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
}

/* Custom Icons */
.cod-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    vertical-align: middle;
    margin-right: 5px;
}

.cod-icon-success {
    color: #46b450;
}

.cod-icon-warning {
    color: #ffb900;
}

.cod-icon-error {
    color: #dc3232;
}

/* Loading Spinner */
.cod-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007cba;
    border-radius: 50%;
    animation: cod-spin 1s linear infinite;
}

@keyframes cod-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Hide spinner by default */
.cod-spinner.hidden {
    display: none;
}
