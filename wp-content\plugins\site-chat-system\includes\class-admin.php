<?php
/**
 * Admin panel functionality for Site Chat System
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Site_Chat_Admin {
    
    /**
     * Initialize admin
     */
    public function init() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('Site Chat', 'site-chat-system'),
            __('Site Chat', 'site-chat-system'),
            'manage_options',
            'site-chat',
            array($this, 'admin_dashboard'),
            'dashicons-format-chat',
            30
        );
        
        add_submenu_page(
            'site-chat',
            __('Dashboard', 'site-chat-system'),
            __('Dashboard', 'site-chat-system'),
            'manage_options',
            'site-chat',
            array($this, 'admin_dashboard')
        );
        
        add_submenu_page(
            'site-chat',
            __('Messages', 'site-chat-system'),
            __('Messages', 'site-chat-system'),
            'manage_options',
            'site-chat-messages',
            array($this, 'admin_messages')
        );
        
        add_submenu_page(
            'site-chat',
            __('Settings', 'site-chat-system'),
            __('Settings', 'site-chat-system'),
            'manage_options',
            'site-chat-settings',
            array($this, 'admin_settings')
        );
    }
    
    /**
     * Admin init
     */
    public function admin_init() {
        register_setting('site_chat_settings', 'site_chat_options');
        
        // General Settings Section
        add_settings_section(
            'site_chat_general',
            __('General Settings', 'site-chat-system'),
            array($this, 'general_settings_callback'),
            'site_chat_settings'
        );
        
        add_settings_field(
            'enable_chat',
            __('Enable Chat', 'site-chat-system'),
            array($this, 'enable_chat_callback'),
            'site_chat_settings',
            'site_chat_general'
        );
        
        add_settings_field(
            'max_message_length',
            __('Max Message Length', 'site-chat-system'),
            array($this, 'max_message_length_callback'),
            'site_chat_settings',
            'site_chat_general'
        );
        
        add_settings_field(
            'message_history_limit',
            __('Message History Limit', 'site-chat-system'),
            array($this, 'message_history_limit_callback'),
            'site_chat_settings',
            'site_chat_general'
        );
        
        add_settings_field(
            'refresh_interval',
            __('Refresh Interval (ms)', 'site-chat-system'),
            array($this, 'refresh_interval_callback'),
            'site_chat_settings',
            'site_chat_general'
        );
        
        // Appearance Settings Section
        add_settings_section(
            'site_chat_appearance',
            __('Appearance Settings', 'site-chat-system'),
            array($this, 'appearance_settings_callback'),
            'site_chat_settings'
        );
        
        add_settings_field(
            'chat_position',
            __('Chat Position', 'site-chat-system'),
            array($this, 'chat_position_callback'),
            'site_chat_settings',
            'site_chat_appearance'
        );
        
        add_settings_field(
            'chat_theme',
            __('Chat Theme', 'site-chat-system'),
            array($this, 'chat_theme_callback'),
            'site_chat_settings',
            'site_chat_appearance'
        );
        
        // Moderation Settings Section
        add_settings_section(
            'site_chat_moderation',
            __('Moderation Settings', 'site-chat-system'),
            array($this, 'moderation_settings_callback'),
            'site_chat_settings'
        );
        
        add_settings_field(
            'auto_delete_messages',
            __('Auto Delete Messages', 'site-chat-system'),
            array($this, 'auto_delete_messages_callback'),
            'site_chat_settings',
            'site_chat_moderation'
        );
        
        add_settings_field(
            'delete_messages_after_days',
            __('Delete Messages After (Days)', 'site-chat-system'),
            array($this, 'delete_messages_after_days_callback'),
            'site_chat_settings',
            'site_chat_moderation'
        );
    }
    
    /**
     * Admin dashboard
     */
    public function admin_dashboard() {
        global $wpdb;
        
        // Get statistics
        $messages_table = $wpdb->prefix . 'chat_messages';
        $rooms_table = $wpdb->prefix . 'chat_rooms';
        $online_users_table = $wpdb->prefix . 'chat_online_users';
        
        $stats = array(
            'total_messages' => $wpdb->get_var("SELECT COUNT(*) FROM $messages_table"),
            'messages_today' => $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $messages_table WHERE DATE(created_date) = %s",
                current_time('Y-m-d')
            )),
            'total_rooms' => $wpdb->get_var("SELECT COUNT(*) FROM $rooms_table WHERE is_active = 1"),
            'online_users' => $wpdb->get_var("SELECT COUNT(*) FROM $online_users_table WHERE last_activity > DATE_SUB(NOW(), INTERVAL 5 MINUTE)"),
        );
        
        // Get recent messages
        $recent_messages = $wpdb->get_results(
            "SELECT m.*, u.display_name, r.name as room_name
             FROM $messages_table m
             INNER JOIN {$wpdb->users} u ON m.user_id = u.ID
             INNER JOIN $rooms_table r ON m.room_id = r.id
             ORDER BY m.created_date DESC
             LIMIT 10"
        );
        
        ?>
        <div class="wrap">
            <h1><?php _e('Site Chat Dashboard', 'site-chat-system'); ?></h1>
            
            <div class="site-chat-dashboard-stats">
                <div class="site-chat-stat-box">
                    <h3><?php echo $stats['total_messages']; ?></h3>
                    <p><?php _e('Total Messages', 'site-chat-system'); ?></p>
                </div>
                <div class="site-chat-stat-box">
                    <h3><?php echo $stats['messages_today']; ?></h3>
                    <p><?php _e('Messages Today', 'site-chat-system'); ?></p>
                </div>
                <div class="site-chat-stat-box">
                    <h3><?php echo $stats['total_rooms']; ?></h3>
                    <p><?php _e('Active Rooms', 'site-chat-system'); ?></p>
                </div>
                <div class="site-chat-stat-box">
                    <h3><?php echo $stats['online_users']; ?></h3>
                    <p><?php _e('Online Users', 'site-chat-system'); ?></p>
                </div>
            </div>
            
            <?php if (!empty($recent_messages)): ?>
            <div class="site-chat-recent-messages">
                <h2><?php _e('Recent Messages', 'site-chat-system'); ?></h2>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('User', 'site-chat-system'); ?></th>
                            <th><?php _e('Room', 'site-chat-system'); ?></th>
                            <th><?php _e('Message', 'site-chat-system'); ?></th>
                            <th><?php _e('Date', 'site-chat-system'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_messages as $message): ?>
                        <tr>
                            <td><?php echo esc_html($message->display_name); ?></td>
                            <td><?php echo esc_html($message->room_name); ?></td>
                            <td><?php echo esc_html(wp_trim_words($message->message, 10)); ?></td>
                            <td><?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($message->created_date)); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
        </div>
        <?php
    }
    
    /**
     * Admin messages page
     */
    public function admin_messages() {
        echo '<div class="wrap"><h1>' . __('Chat Messages', 'site-chat-system') . '</h1>';
        echo '<p>' . __('Message management functionality will be implemented here.', 'site-chat-system') . '</p></div>';
    }
    
    /**
     * Admin settings page
     */
    public function admin_settings() {
        ?>
        <div class="wrap">
            <h1><?php _e('Site Chat Settings', 'site-chat-system'); ?></h1>
            <form method="post" action="options.php">
                <?php
                settings_fields('site_chat_settings');
                do_settings_sections('site_chat_settings');
                submit_button();
                ?>
            </form>
        </div>
        <?php
    }
    
    /**
     * Settings callbacks
     */
    public function general_settings_callback() {
        echo '<p>' . __('Configure general chat settings.', 'site-chat-system') . '</p>';
    }
    
    public function appearance_settings_callback() {
        echo '<p>' . __('Configure chat appearance and positioning.', 'site-chat-system') . '</p>';
    }
    
    public function moderation_settings_callback() {
        echo '<p>' . __('Configure moderation and cleanup settings.', 'site-chat-system') . '</p>';
    }
    
    public function enable_chat_callback() {
        $options = get_option('site_chat_options', array());
        $value = $options['enable_chat'] ?? true;
        echo '<input type="checkbox" name="site_chat_options[enable_chat]" value="1" ' . checked(1, $value, false) . ' />';
        echo '<label>' . __('Enable the chat system site-wide', 'site-chat-system') . '</label>';
    }
    
    public function max_message_length_callback() {
        $options = get_option('site_chat_options', array());
        $value = $options['max_message_length'] ?? 500;
        echo '<input type="number" name="site_chat_options[max_message_length]" value="' . $value . '" min="50" max="2000" />';
        echo '<p class="description">' . __('Maximum number of characters per message.', 'site-chat-system') . '</p>';
    }
    
    public function message_history_limit_callback() {
        $options = get_option('site_chat_options', array());
        $value = $options['message_history_limit'] ?? 50;
        echo '<input type="number" name="site_chat_options[message_history_limit]" value="' . $value . '" min="10" max="200" />';
        echo '<p class="description">' . __('Number of messages to load in chat history.', 'site-chat-system') . '</p>';
    }
    
    public function refresh_interval_callback() {
        $options = get_option('site_chat_options', array());
        $value = $options['refresh_interval'] ?? 3000;
        echo '<input type="number" name="site_chat_options[refresh_interval]" value="' . $value . '" min="1000" max="10000" step="500" />';
        echo '<p class="description">' . __('How often to check for new messages (in milliseconds).', 'site-chat-system') . '</p>';
    }
    
    public function chat_position_callback() {
        $options = get_option('site_chat_options', array());
        $value = $options['chat_position'] ?? 'bottom-right';
        $positions = array(
            'bottom-right' => __('Bottom Right', 'site-chat-system'),
            'bottom-left' => __('Bottom Left', 'site-chat-system'),
            'top-right' => __('Top Right', 'site-chat-system'),
            'top-left' => __('Top Left', 'site-chat-system'),
        );
        
        echo '<select name="site_chat_options[chat_position]">';
        foreach ($positions as $key => $label) {
            echo '<option value="' . $key . '"' . selected($value, $key, false) . '>' . $label . '</option>';
        }
        echo '</select>';
    }
    
    public function chat_theme_callback() {
        $options = get_option('site_chat_options', array());
        $value = $options['chat_theme'] ?? 'default';
        $themes = array(
            'default' => __('Default', 'site-chat-system'),
            'dark' => __('Dark', 'site-chat-system'),
            'light' => __('Light', 'site-chat-system'),
        );
        
        echo '<select name="site_chat_options[chat_theme]">';
        foreach ($themes as $key => $label) {
            echo '<option value="' . $key . '"' . selected($value, $key, false) . '>' . $label . '</option>';
        }
        echo '</select>';
    }
    
    public function auto_delete_messages_callback() {
        $options = get_option('site_chat_options', array());
        $value = $options['auto_delete_messages'] ?? false;
        echo '<input type="checkbox" name="site_chat_options[auto_delete_messages]" value="1" ' . checked(1, $value, false) . ' />';
        echo '<label>' . __('Automatically delete old messages', 'site-chat-system') . '</label>';
    }
    
    public function delete_messages_after_days_callback() {
        $options = get_option('site_chat_options', array());
        $value = $options['delete_messages_after_days'] ?? 30;
        echo '<input type="number" name="site_chat_options[delete_messages_after_days]" value="' . $value . '" min="1" max="365" />';
        echo '<p class="description">' . __('Delete messages older than this many days.', 'site-chat-system') . '</p>';
    }
}
