# Call of Duty Clan League WordPress Plugin

A comprehensive WordPress plugin for managing Call of Duty clan leagues with team registration, match reporting, and league management.

## Features

### Core Functionality

- **Clan Registration System**
  - Create and manage clans with custom names, tags, and logos
  - Invite system for adding members to clans
  - Role-based permissions (Captain, Member)
  - User validation and approval workflows

- **League Management**
  - Create and manage seasons with start/end dates
  - Team registration and approval system
  - Automatic league table calculations (3 points for win, 0 for loss)
  - Round-robin fixture generation
  - Season status management (Upcoming, Active, Completed)

- **Match Reporting**
  - Team captains can submit match results
  - Admin approval workflow for results
  - Optional screenshot upload for proof
  - Match status tracking (Scheduled, Pending Approval, Completed)
  - Result override capabilities for admins

- **Frontend Display**
  - Shortcodes for displaying league tables, fixtures, and results
  - Team/clan profiles with match history
  - Responsive design with clean styling
  - Public viewing of league information

- **Admin Panel**
  - Comprehensive dashboard with statistics
  - Season controls and team approvals
  - Match result management
  - User role and permission management
  - Plugin settings and configuration

## Installation

1. Upload the plugin files to `/wp-content/plugins/cod-clan-league/`
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Configure plugin settings in the admin panel
4. Create your first season and start managing your league!

## Shortcodes

### League Table
```
[cod_league_table season_id="1" limit="10" show_logo="true" show_stats="true"]
```

### Fixtures List
```
[cod_fixtures season_id="1" status="scheduled" limit="10" show_date="true"]
```

### Match Results
```
[cod_results season_id="1" limit="10" show_date="true" show_score="true"]
```

### Clan Profile
```
[cod_clan_profile clan_id="1" show_members="true" show_matches="true" match_limit="5"]
```

### Clan List
```
[cod_clan_list status="active" limit="0" show_logo="true" show_members="true"]
```

### Season Information
```
[cod_season_info season_id="1" show_teams="true" show_dates="true"]
```

### Match Report Form
```
[cod_match_report_form match_id="1"]
```

## User Roles

The plugin creates custom user roles with specific capabilities:

- **League Administrator**: Full control over all league functions
- **Clan Captain**: Can manage their clan and report match results
- **Clan Member**: Basic clan membership with viewing permissions

## Database Structure

The plugin creates the following custom tables:

- `cod_clans` - Clan information
- `cod_clan_members` - Clan membership data
- `cod_seasons` - Season details
- `cod_season_teams` - Team registrations for seasons
- `cod_matches` - Match information and results
- `cod_match_events` - Detailed match events (future use)
- `cod_clan_invitations` - Invitation system data

## Configuration Options

Access plugin settings via **COD League > Settings**:

- Points for Win/Loss
- Allow/Disallow draws
- Require match screenshots
- Auto-approve results
- Maximum/Minimum clan sizes
- And more...

## Security Features

- Comprehensive input validation and sanitization
- Nonce verification for all AJAX requests
- Role-based access control
- Rate limiting for API requests
- Security event logging
- File upload validation

## Development

### File Structure
```
cod-clan-league/
├── cod-clan-league.php          # Main plugin file
├── includes/                    # Core PHP classes
│   ├── class-database.php       # Database management
│   ├── class-post-types.php     # Custom post types
│   ├── class-user-roles.php     # User roles and capabilities
│   ├── class-clan-manager.php   # Clan functionality
│   ├── class-league-manager.php # League management
│   ├── class-match-manager.php  # Match reporting
│   ├── class-frontend.php       # Frontend display
│   ├── class-admin.php          # Admin interface
│   ├── class-security.php       # Security utilities
│   └── class-utilities.php      # Helper functions
├── assets/                      # CSS and JavaScript
│   ├── css/
│   │   ├── frontend.css         # Frontend styles
│   │   └── admin.css            # Admin styles
│   └── js/
│       ├── frontend.js          # Frontend JavaScript
│       └── admin.js             # Admin JavaScript
└── README.md                    # This file
```

### Hooks and Filters

The plugin provides various action hooks and filters for customization:

**Actions:**
- `cod_clan_created` - Fired when a clan is created
- `cod_season_created` - Fired when a season is created
- `cod_match_result_reported` - Fired when a match result is reported
- `cod_team_registered` - Fired when a team registers for a season

**Filters:**
- `cod_clan_league_options` - Filter plugin options
- `cod_league_table_data` - Filter league table data
- `cod_match_result_data` - Filter match result data

## Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher
- MySQL 5.6 or higher

## Support

For support and feature requests, please contact the plugin developer or submit issues through the appropriate channels.

## License

This plugin is licensed under the GPL v2 or later.

## Changelog

### Version 1.0.0
- Initial release
- Complete clan league management system
- Frontend shortcodes and display
- Admin panel with full functionality
- Security and validation features
- Responsive design and styling

## Credits

Developed for Call of Duty clan league management with WordPress best practices and modern development standards.
