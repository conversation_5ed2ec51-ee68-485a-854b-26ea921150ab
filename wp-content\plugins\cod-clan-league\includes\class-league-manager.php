<?php
/**
 * League and season management functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class COD_Clan_League_League_Manager {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('wp_ajax_cod_create_season', array($this, 'ajax_create_season'));
        add_action('wp_ajax_cod_register_team', array($this, 'ajax_register_team'));
        add_action('wp_ajax_cod_approve_team', array($this, 'ajax_approve_team'));
        add_action('wp_ajax_cod_generate_fixtures', array($this, 'ajax_generate_fixtures'));
        add_action('wp_ajax_cod_update_league_table', array($this, 'ajax_update_league_table'));
    }
    
    /**
     * Create a new season
     */
    public function create_season($data) {
        global $wpdb;
        
        // Validate required fields
        if (empty($data['name']) || empty($data['start_date']) || empty($data['end_date'])) {
            return new WP_Error('missing_data', __('Missing required season information.', 'cod-clan-league'));
        }
        
        // Validate dates
        $start_date = strtotime($data['start_date']);
        $end_date = strtotime($data['end_date']);
        
        if ($start_date >= $end_date) {
            return new WP_Error('invalid_dates', __('End date must be after start date.', 'cod-clan-league'));
        }
        
        $seasons_table = $wpdb->prefix . 'cod_seasons';
        
        // Check for overlapping seasons
        $overlapping = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $seasons_table 
             WHERE status IN ('upcoming', 'active') 
             AND ((start_date <= %s AND end_date >= %s) OR (start_date <= %s AND end_date >= %s))",
            $data['start_date'], $data['start_date'],
            $data['end_date'], $data['end_date']
        ));
        
        if ($overlapping) {
            return new WP_Error('overlapping_season', __('Season dates overlap with an existing season.', 'cod-clan-league'));
        }
        
        // Insert season
        $season_data = array(
            'name' => sanitize_text_field($data['name']),
            'description' => sanitize_textarea_field($data['description'] ?? ''),
            'start_date' => $data['start_date'],
            'end_date' => $data['end_date'],
            'status' => 'upcoming',
            'max_teams' => intval($data['max_teams'] ?? 16),
            'created_date' => current_time('mysql'),
        );
        
        $result = $wpdb->insert($seasons_table, $season_data);
        
        if ($result === false) {
            return new WP_Error('db_error', __('Failed to create season.', 'cod-clan-league'));
        }
        
        $season_id = $wpdb->insert_id;
        
        do_action('cod_season_created', $season_id, $data);
        
        return $season_id;
    }
    
    /**
     * Register team for season
     */
    public function register_team($season_id, $clan_id, $user_id = null) {
        global $wpdb;
        
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        // Validate season exists and is open for registration
        $season = $this->get_season($season_id);
        if (!$season) {
            return new WP_Error('season_not_found', __('Season not found.', 'cod-clan-league'));
        }
        
        if ($season->status !== 'upcoming') {
            return new WP_Error('registration_closed', __('Registration is closed for this season.', 'cod-clan-league'));
        }
        
        // Check if user can register team
        $user_roles = new COD_Clan_League_User_Roles();
        if (!$user_roles->user_can_manage_clan($user_id, $clan_id) && !current_user_can('manage_cod_teams')) {
            return new WP_Error('no_permission', __('You do not have permission to register this team.', 'cod-clan-league'));
        }
        
        // Check if clan exists and is active
        $clan_manager = new COD_Clan_League_Clan_Manager();
        $clan = $clan_manager->get_clan($clan_id);
        if (!$clan || $clan->status !== 'active') {
            return new WP_Error('clan_not_active', __('Clan is not active.', 'cod-clan-league'));
        }
        
        // Check if team is already registered
        $season_teams_table = $wpdb->prefix . 'cod_season_teams';
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $season_teams_table WHERE season_id = %d AND clan_id = %d",
            $season_id,
            $clan_id
        ));
        
        if ($existing) {
            return new WP_Error('already_registered', __('Team is already registered for this season.', 'cod-clan-league'));
        }
        
        // Check season capacity
        $current_teams = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $season_teams_table WHERE season_id = %d AND status = 'approved'",
            $season_id
        ));
        
        if ($current_teams >= $season->max_teams) {
            return new WP_Error('season_full', __('Season has reached maximum team capacity.', 'cod-clan-league'));
        }
        
        // Check minimum clan size
        $options = get_option('cod_clan_league_options', array());
        $min_size = $options['min_clan_size'] ?? 3;
        
        $clan_size = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}cod_clan_members WHERE clan_id = %d AND status = 'active'",
            $clan_id
        ));
        
        if ($clan_size < $min_size) {
            return new WP_Error('clan_too_small', sprintf(__('Clan must have at least %d active members.', 'cod-clan-league'), $min_size));
        }
        
        // Register team
        $team_data = array(
            'season_id' => $season_id,
            'clan_id' => $clan_id,
            'status' => 'pending',
            'points' => 0,
            'matches_played' => 0,
            'matches_won' => 0,
            'matches_lost' => 0,
            'joined_date' => current_time('mysql'),
        );
        
        $result = $wpdb->insert($season_teams_table, $team_data);
        
        if ($result === false) {
            return new WP_Error('db_error', __('Failed to register team.', 'cod-clan-league'));
        }
        
        do_action('cod_team_registered', $season_id, $clan_id, $wpdb->insert_id);
        
        return $wpdb->insert_id;
    }
    
    /**
     * Approve team registration
     */
    public function approve_team($team_id, $approved = true) {
        global $wpdb;
        
        if (!current_user_can('manage_cod_teams')) {
            return new WP_Error('no_permission', __('You do not have permission to approve teams.', 'cod-clan-league'));
        }
        
        $season_teams_table = $wpdb->prefix . 'cod_season_teams';
        
        $team = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $season_teams_table WHERE id = %d",
            $team_id
        ));
        
        if (!$team) {
            return new WP_Error('team_not_found', __('Team registration not found.', 'cod-clan-league'));
        }
        
        $new_status = $approved ? 'approved' : 'declined';
        
        $result = $wpdb->update(
            $season_teams_table,
            array('status' => $new_status),
            array('id' => $team_id)
        );
        
        if ($result === false) {
            return new WP_Error('db_error', __('Failed to update team status.', 'cod-clan-league'));
        }
        
        do_action('cod_team_approval_changed', $team_id, $new_status, $team->season_id, $team->clan_id);
        
        return true;
    }
    
    /**
     * Generate fixtures for a season (round-robin)
     */
    public function generate_fixtures($season_id) {
        global $wpdb;
        
        if (!current_user_can('manage_cod_seasons')) {
            return new WP_Error('no_permission', __('You do not have permission to generate fixtures.', 'cod-clan-league'));
        }
        
        // Get approved teams
        $season_teams_table = $wpdb->prefix . 'cod_season_teams';
        $teams = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $season_teams_table WHERE season_id = %d AND status = 'approved' ORDER BY id",
            $season_id
        ));
        
        if (count($teams) < 2) {
            return new WP_Error('insufficient_teams', __('At least 2 teams are required to generate fixtures.', 'cod-clan-league'));
        }
        
        // Check if fixtures already exist
        $matches_table = $wpdb->prefix . 'cod_matches';
        $existing_matches = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $matches_table WHERE season_id = %d",
            $season_id
        ));
        
        if ($existing_matches > 0) {
            return new WP_Error('fixtures_exist', __('Fixtures already exist for this season.', 'cod-clan-league'));
        }
        
        // Generate round-robin fixtures
        $fixtures = $this->generate_round_robin_fixtures($teams);
        
        // Get season dates for scheduling
        $season = $this->get_season($season_id);
        $start_date = strtotime($season->start_date);
        $end_date = strtotime($season->end_date);
        $total_days = ($end_date - $start_date) / (24 * 60 * 60);
        $matches_per_week = 2; // Configurable
        
        $match_count = 0;
        foreach ($fixtures as $round => $matches) {
            foreach ($matches as $match) {
                $days_offset = ($match_count * 7) / $matches_per_week;
                $match_date = date('Y-m-d H:i:s', $start_date + ($days_offset * 24 * 60 * 60));
                
                $match_data = array(
                    'season_id' => $season_id,
                    'home_team_id' => $match['home'],
                    'away_team_id' => $match['away'],
                    'scheduled_date' => $match_date,
                    'status' => 'scheduled',
                    'created_date' => current_time('mysql'),
                );
                
                $wpdb->insert($matches_table, $match_data);
                $match_count++;
            }
        }
        
        do_action('cod_fixtures_generated', $season_id, count($fixtures));
        
        return count($fixtures);
    }
    
    /**
     * Generate round-robin fixtures
     */
    private function generate_round_robin_fixtures($teams) {
        $team_count = count($teams);
        $fixtures = array();
        
        // If odd number of teams, add a "bye" team
        if ($team_count % 2 !== 0) {
            $teams[] = (object) array('id' => null); // Bye team
            $team_count++;
        }
        
        $rounds = $team_count - 1;
        $matches_per_round = $team_count / 2;
        
        for ($round = 0; $round < $rounds; $round++) {
            $fixtures[$round + 1] = array();
            
            for ($match = 0; $match < $matches_per_round; $match++) {
                $home_index = ($round + $match) % ($team_count - 1);
                $away_index = ($team_count - 1 - $match + $round) % ($team_count - 1);
                
                // Last team stays fixed
                if ($match === 0) {
                    $away_index = $team_count - 1;
                }
                
                $home_team = $teams[$home_index];
                $away_team = $teams[$away_index];
                
                // Skip matches involving bye team
                if ($home_team->id && $away_team->id) {
                    $fixtures[$round + 1][] = array(
                        'home' => $home_team->id,
                        'away' => $away_team->id,
                    );
                }
            }
        }
        
        return $fixtures;
    }
    
    /**
     * Update league table based on match results
     */
    public function update_league_table($season_id) {
        global $wpdb;
        
        $season_teams_table = $wpdb->prefix . 'cod_season_teams';
        $matches_table = $wpdb->prefix . 'cod_matches';
        
        // Get all teams in season
        $teams = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $season_teams_table WHERE season_id = %d AND status = 'approved'",
            $season_id
        ));
        
        // Get completed matches
        $matches = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $matches_table WHERE season_id = %d AND status = 'completed'",
            $season_id
        ));
        
        $options = get_option('cod_clan_league_options', array());
        $points_for_win = $options['points_for_win'] ?? 3;
        $points_for_loss = $options['points_for_loss'] ?? 0;
        
        // Reset team stats
        foreach ($teams as $team) {
            $stats = array(
                'points' => 0,
                'matches_played' => 0,
                'matches_won' => 0,
                'matches_lost' => 0,
            );
            
            // Calculate stats from matches
            foreach ($matches as $match) {
                if ($match->home_team_id == $team->id || $match->away_team_id == $team->id) {
                    $stats['matches_played']++;
                    
                    if ($match->winner_team_id == $team->id) {
                        $stats['matches_won']++;
                        $stats['points'] += $points_for_win;
                    } else {
                        $stats['matches_lost']++;
                        $stats['points'] += $points_for_loss;
                    }
                }
            }
            
            // Update team stats
            $wpdb->update(
                $season_teams_table,
                $stats,
                array('id' => $team->id)
            );
        }
        
        do_action('cod_league_table_updated', $season_id);
        
        return true;
    }
    
    /**
     * Get season by ID
     */
    public function get_season($season_id) {
        global $wpdb;
        
        $seasons_table = $wpdb->prefix . 'cod_seasons';
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $seasons_table WHERE id = %d",
            $season_id
        ));
    }
    
    /**
     * Get league table for season
     */
    public function get_league_table($season_id) {
        global $wpdb;
        
        $season_teams_table = $wpdb->prefix . 'cod_season_teams';
        $clans_table = $wpdb->prefix . 'cod_clans';
        
        return $wpdb->get_results($wpdb->prepare(
            "SELECT st.*, c.name as clan_name, c.tag as clan_tag, c.logo_url
             FROM $season_teams_table st
             INNER JOIN $clans_table c ON st.clan_id = c.id
             WHERE st.season_id = %d AND st.status = 'approved'
             ORDER BY st.points DESC, st.matches_won DESC, c.name ASC",
            $season_id
        ));
    }
    
    /**
     * Get season teams
     */
    public function get_season_teams($season_id, $status = 'approved') {
        global $wpdb;
        
        $season_teams_table = $wpdb->prefix . 'cod_season_teams';
        $clans_table = $wpdb->prefix . 'cod_clans';
        
        $query = "SELECT st.*, c.name as clan_name, c.tag as clan_tag, c.logo_url
                  FROM $season_teams_table st
                  INNER JOIN $clans_table c ON st.clan_id = c.id
                  WHERE st.season_id = %d";
        
        $params = array($season_id);
        
        if ($status) {
            $query .= " AND st.status = %s";
            $params[] = $status;
        }
        
        $query .= " ORDER BY c.name ASC";
        
        return $wpdb->get_results($wpdb->prepare($query, $params));
    }

    /**
     * AJAX: Create season
     */
    public function ajax_create_season() {
        check_ajax_referer('cod_clan_league_admin_nonce', 'nonce');

        if (!current_user_can('manage_cod_seasons')) {
            wp_send_json_error(__('You do not have permission to create seasons.', 'cod-clan-league'));
        }

        $data = array(
            'name' => sanitize_text_field($_POST['name'] ?? ''),
            'description' => sanitize_textarea_field($_POST['description'] ?? ''),
            'start_date' => sanitize_text_field($_POST['start_date'] ?? ''),
            'end_date' => sanitize_text_field($_POST['end_date'] ?? ''),
            'max_teams' => intval($_POST['max_teams'] ?? 16),
        );

        $result = $this->create_season($data);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(array(
                'season_id' => $result,
                'message' => __('Season created successfully!', 'cod-clan-league'),
            ));
        }
    }

    /**
     * AJAX: Register team
     */
    public function ajax_register_team() {
        check_ajax_referer('cod_clan_league_nonce', 'nonce');

        if (!is_user_logged_in()) {
            wp_send_json_error(__('You must be logged in.', 'cod-clan-league'));
        }

        $season_id = intval($_POST['season_id'] ?? 0);
        $clan_id = intval($_POST['clan_id'] ?? 0);

        $result = $this->register_team($season_id, $clan_id);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(__('Team registered successfully!', 'cod-clan-league'));
        }
    }

    /**
     * AJAX: Approve team
     */
    public function ajax_approve_team() {
        check_ajax_referer('cod_clan_league_admin_nonce', 'nonce');

        if (!current_user_can('manage_cod_teams')) {
            wp_send_json_error(__('You do not have permission to approve teams.', 'cod-clan-league'));
        }

        $team_id = intval($_POST['team_id'] ?? 0);
        $approved = $_POST['approved'] === 'true';

        $result = $this->approve_team($team_id, $approved);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            $message = $approved
                ? __('Team approved successfully!', 'cod-clan-league')
                : __('Team declined.', 'cod-clan-league');
            wp_send_json_success($message);
        }
    }

    /**
     * AJAX: Generate fixtures
     */
    public function ajax_generate_fixtures() {
        check_ajax_referer('cod_clan_league_admin_nonce', 'nonce');

        if (!current_user_can('manage_cod_seasons')) {
            wp_send_json_error(__('You do not have permission to generate fixtures.', 'cod-clan-league'));
        }

        $season_id = intval($_POST['season_id'] ?? 0);

        $result = $this->generate_fixtures($season_id);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(sprintf(__('%d rounds of fixtures generated!', 'cod-clan-league'), $result));
        }
    }

    /**
     * AJAX: Update league table
     */
    public function ajax_update_league_table() {
        check_ajax_referer('cod_clan_league_admin_nonce', 'nonce');

        if (!current_user_can('manage_cod_matches')) {
            wp_send_json_error(__('You do not have permission to update league table.', 'cod-clan-league'));
        }

        $season_id = intval($_POST['season_id'] ?? 0);

        $result = $this->update_league_table($season_id);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(__('League table updated successfully!', 'cod-clan-league'));
        }
    }
}
