/**
 * Site Chat System Admin JavaScript
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        SiteChatAdmin.init();
    });

    // Main admin object
    window.SiteChatAdmin = {
        
        // Initialize admin functionality
        init: function() {
            this.bindEvents();
            this.initComponents();
            this.loadDashboardData();
        },

        // Bind event handlers
        bindEvents: function() {
            // Settings form handling
            $(document).on('submit', '#site-chat-settings-form', this.handleSettingsSubmit);
            
            // Message management
            $(document).on('click', '.site-chat-delete-message', this.handleDeleteMessage);
            $(document).on('click', '.site-chat-ban-user', this.handleBanUser);
            
            // Bulk actions
            $(document).on('click', '.site-chat-bulk-action', this.handleBulkAction);
            
            // Tab switching
            $(document).on('click', '.site-chat-tab-button', this.handleTabSwitch);
            
            // Settings preview
            $(document).on('change', '#site-chat-settings input, #site-chat-settings select', this.updatePreview);
            
            // Clear chat history
            $(document).on('click', '.site-chat-clear-history', this.handleClearHistory);
            
            // Export chat data
            $(document).on('click', '.site-chat-export-data', this.handleExportData);
            
            // Test notifications
            $(document).on('click', '.site-chat-test-notification', this.handleTestNotification);
        },

        // Initialize components
        initComponents: function() {
            this.initDataTables();
            this.initCharts();
            this.initTooltips();
            this.initColorPickers();
        },

        // Load dashboard data
        loadDashboardData: function() {
            this.updateStats();
            this.loadRecentActivity();
        },

        // Handle settings form submission
        handleSettingsSubmit: function(e) {
            var $form = $(this);
            var $submitBtn = $form.find('input[type="submit"]');
            var originalText = $submitBtn.val();
            
            $submitBtn.val('Saving...').prop('disabled', true);
            
            // Re-enable button after form submission
            setTimeout(function() {
                $submitBtn.val(originalText).prop('disabled', false);
            }, 2000);
        },

        // Handle message deletion
        handleDeleteMessage: function(e) {
            e.preventDefault();
            
            var messageId = $(this).data('message-id');
            var $row = $(this).closest('tr');
            
            if (!confirm('Are you sure you want to delete this message?')) {
                return;
            }
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'site_chat_delete_message',
                    nonce: siteChatAdmin.nonce,
                    message_id: messageId
                },
                success: function(response) {
                    if (response.success) {
                        $row.fadeOut(function() {
                            $(this).remove();
                        });
                        SiteChatAdmin.showNotification('Message deleted successfully', 'success');
                    } else {
                        SiteChatAdmin.showNotification(response.data, 'error');
                    }
                },
                error: function() {
                    SiteChatAdmin.showNotification('An error occurred', 'error');
                }
            });
        },

        // Handle user ban
        handleBanUser: function(e) {
            e.preventDefault();
            
            var userId = $(this).data('user-id');
            var userName = $(this).data('user-name');
            var $btn = $(this);
            
            if (!confirm('Are you sure you want to ban user "' + userName + '"?')) {
                return;
            }
            
            $btn.prop('disabled', true);
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'site_chat_ban_user',
                    nonce: siteChatAdmin.nonce,
                    user_id: userId
                },
                success: function(response) {
                    if (response.success) {
                        $btn.text('Banned').addClass('button-disabled');
                        SiteChatAdmin.showNotification('User banned successfully', 'success');
                    } else {
                        $btn.prop('disabled', false);
                        SiteChatAdmin.showNotification(response.data, 'error');
                    }
                },
                error: function() {
                    $btn.prop('disabled', false);
                    SiteChatAdmin.showNotification('An error occurred', 'error');
                }
            });
        },

        // Handle bulk actions
        handleBulkAction: function(e) {
            e.preventDefault();
            
            var action = $(this).data('action');
            var $checkboxes = $('.site-chat-bulk-checkbox:checked');
            
            if ($checkboxes.length === 0) {
                alert('Please select items to perform bulk action.');
                return;
            }
            
            var confirmMessage = 'Are you sure you want to ' + action + ' ' + $checkboxes.length + ' item(s)?';
            if (!confirm(confirmMessage)) {
                return;
            }
            
            var ids = [];
            $checkboxes.each(function() {
                ids.push($(this).val());
            });
            
            var $btn = $(this);
            $btn.prop('disabled', true);
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'site_chat_bulk_action',
                    nonce: siteChatAdmin.nonce,
                    bulk_action: action,
                    ids: ids
                },
                success: function(response) {
                    if (response.success) {
                        SiteChatAdmin.showNotification(response.data, 'success');
                        location.reload();
                    } else {
                        SiteChatAdmin.showNotification(response.data, 'error');
                    }
                },
                error: function() {
                    SiteChatAdmin.showNotification('An error occurred', 'error');
                },
                complete: function() {
                    $btn.prop('disabled', false);
                }
            });
        },

        // Handle tab switching
        handleTabSwitch: function(e) {
            e.preventDefault();
            
            var $tab = $(this);
            var target = $tab.data('tab');
            var $container = $tab.closest('.site-chat-settings-tabs');
            
            // Update active tab
            $container.find('.site-chat-tab-button').removeClass('active');
            $tab.addClass('active');
            
            // Update active content
            $container.find('.site-chat-tab-content').removeClass('active');
            $('#' + target).addClass('active');
        },

        // Update settings preview
        updatePreview: function() {
            var position = $('#chat_position').val() || 'bottom-right';
            var theme = $('#chat_theme').val() || 'default';
            
            var $preview = $('.site-chat-preview-widget');
            $preview.removeClass('site-chat-position-bottom-left site-chat-position-bottom-right site-chat-position-top-left site-chat-position-top-right');
            $preview.addClass('site-chat-position-' + position);
            
            $preview.removeClass('site-chat-theme-default site-chat-theme-dark site-chat-theme-light');
            $preview.addClass('site-chat-theme-' + theme);
        },

        // Handle clear chat history
        handleClearHistory: function(e) {
            e.preventDefault();
            
            if (!confirm('Are you sure you want to clear all chat history? This action cannot be undone.')) {
                return;
            }
            
            var $btn = $(this);
            $btn.prop('disabled', true).text('Clearing...');
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'site_chat_clear_history',
                    nonce: siteChatAdmin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        SiteChatAdmin.showNotification('Chat history cleared successfully', 'success');
                        location.reload();
                    } else {
                        SiteChatAdmin.showNotification(response.data, 'error');
                    }
                },
                error: function() {
                    SiteChatAdmin.showNotification('An error occurred', 'error');
                },
                complete: function() {
                    $btn.prop('disabled', false).text('Clear History');
                }
            });
        },

        // Handle data export
        handleExportData: function(e) {
            e.preventDefault();
            
            var $btn = $(this);
            $btn.prop('disabled', true).text('Exporting...');
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'site_chat_export_data',
                    nonce: siteChatAdmin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Trigger download
                        var link = document.createElement('a');
                        link.href = response.data.download_url;
                        link.download = response.data.filename;
                        link.click();
                        
                        SiteChatAdmin.showNotification('Data exported successfully', 'success');
                    } else {
                        SiteChatAdmin.showNotification(response.data, 'error');
                    }
                },
                error: function() {
                    SiteChatAdmin.showNotification('An error occurred', 'error');
                },
                complete: function() {
                    $btn.prop('disabled', false).text('Export Data');
                }
            });
        },

        // Handle test notification
        handleTestNotification: function(e) {
            e.preventDefault();
            
            SiteChatAdmin.showNotification('This is a test notification', 'info');
        },

        // Initialize data tables
        initDataTables: function() {
            if ($.fn.DataTable) {
                $('.site-chat-admin-table').DataTable({
                    responsive: true,
                    pageLength: 25,
                    order: [[1, 'desc']],
                    columnDefs: [
                        { orderable: false, targets: [0, -1] }
                    ],
                    language: {
                        search: 'Search:',
                        lengthMenu: 'Show _MENU_ entries per page',
                        info: 'Showing _START_ to _END_ of _TOTAL_ entries',
                        paginate: {
                            first: 'First',
                            last: 'Last',
                            next: 'Next',
                            previous: 'Previous'
                        }
                    }
                });
            }
        },

        // Initialize charts
        initCharts: function() {
            if (typeof Chart !== 'undefined') {
                this.initMessageChart();
                this.initUserChart();
            }
        },

        // Initialize message activity chart
        initMessageChart: function() {
            var ctx = document.getElementById('site-chat-message-chart');
            if (!ctx) return;
            
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                    datasets: [{
                        label: 'Messages',
                        data: [12, 19, 3, 5, 2, 3, 9],
                        borderColor: '#007cba',
                        backgroundColor: 'rgba(0, 124, 186, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        },

        // Initialize user activity chart
        initUserChart: function() {
            var ctx = document.getElementById('site-chat-user-chart');
            if (!ctx) return;
            
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Online', 'Away', 'Offline'],
                    datasets: [{
                        data: [30, 10, 60],
                        backgroundColor: ['#28a745', '#ffc107', '#6c757d']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        },

        // Initialize tooltips
        initTooltips: function() {
            $('[data-tooltip]').each(function() {
                $(this).attr('title', $(this).data('tooltip'));
            });
        },

        // Initialize color pickers
        initColorPickers: function() {
            if ($.fn.wpColorPicker) {
                $('.site-chat-color-picker').wpColorPicker();
            }
        },

        // Update dashboard stats
        updateStats: function() {
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'site_chat_get_stats',
                    nonce: siteChatAdmin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        var stats = response.data;
                        $('.site-chat-stat-messages .site-chat-stat-number').text(stats.total_messages);
                        $('.site-chat-stat-users .site-chat-stat-number').text(stats.online_users);
                        $('.site-chat-stat-rooms .site-chat-stat-number').text(stats.total_rooms);
                    }
                }
            });
        },

        // Load recent activity
        loadRecentActivity: function() {
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'site_chat_get_recent_activity',
                    nonce: siteChatAdmin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        var html = '';
                        response.data.forEach(function(activity) {
                            html += '<div class="site-chat-activity-item">';
                            html += '<span class="site-chat-activity-user">' + activity.user_name + '</span>';
                            html += '<span class="site-chat-activity-action">' + activity.action + '</span>';
                            html += '<span class="site-chat-activity-time">' + activity.time + '</span>';
                            html += '</div>';
                        });
                        $('.site-chat-recent-activity-list').html(html);
                    }
                }
            });
        },

        // Show notification
        showNotification: function(message, type) {
            type = type || 'info';
            
            var $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
            
            $('.wrap h1').after($notice);
            
            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $notice.fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
        },

        // Show loading spinner
        showLoading: function($element) {
            $element.append('<span class="site-chat-loading"></span>');
        },

        // Hide loading spinner
        hideLoading: function($element) {
            $element.find('.site-chat-loading').remove();
        },

        // Utility: Format number
        formatNumber: function(num, decimals) {
            decimals = decimals || 0;
            return parseFloat(num).toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        },

        // Utility: Debounce function
        debounce: function(func, wait, immediate) {
            var timeout;
            return function() {
                var context = this, args = arguments;
                var later = function() {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                var callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };
        }
    };

})(jQuery);
