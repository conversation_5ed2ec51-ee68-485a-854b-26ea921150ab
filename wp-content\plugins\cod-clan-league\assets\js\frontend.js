/**
 * COD Clan League Frontend JavaScript
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        CODClanLeague.init();
    });

    // Main plugin object
    window.CODClanLeague = {
        
        // Initialize all functionality
        init: function() {
            this.bindEvents();
            this.initComponents();
        },

        // Bind event handlers
        bindEvents: function() {
            // Match report form submission
            $(document).on('submit', '#cod-match-report-form', this.handleMatchReportSubmission);
            
            // Clan creation form
            $(document).on('submit', '#cod-clan-creation-form', this.handleClanCreation);
            
            // Team registration
            $(document).on('click', '.cod-register-team', this.handleTeamRegistration);
            
            // Invitation responses
            $(document).on('click', '.cod-respond-invitation', this.handleInvitationResponse);
            
            // File upload handling
            $(document).on('change', 'input[type="file"]', this.handleFileUpload);
            
            // Confirmation dialogs
            $(document).on('click', '[data-confirm]', this.handleConfirmation);
        },

        // Initialize components
        initComponents: function() {
            this.initTooltips();
            this.initTabs();
            this.initModals();
            this.initDataTables();
        },

        // Handle match report form submission
        handleMatchReportSubmission: function(e) {
            e.preventDefault();
            
            var $form = $(this);
            var $submitBtn = $form.find('button[type="submit"]');
            var $messages = $('#cod-form-messages');
            
            // Disable submit button
            $submitBtn.prop('disabled', true).text('Submitting...');
            
            // Prepare form data
            var formData = new FormData(this);
            formData.append('action', 'cod_report_match_result');
            
            // Handle file upload if present
            var fileInput = $form.find('input[type="file"]')[0];
            if (fileInput && fileInput.files.length > 0) {
                // First upload the file
                CODClanLeague.uploadFile(fileInput.files[0], function(response) {
                    if (response.success) {
                        formData.append('screenshot_url', response.data.url);
                        CODClanLeague.submitMatchReport(formData, $submitBtn, $messages);
                    } else {
                        CODClanLeague.showMessage($messages, response.data, 'error');
                        $submitBtn.prop('disabled', false).text('Submit Result');
                    }
                });
            } else {
                CODClanLeague.submitMatchReport(formData, $submitBtn, $messages);
            }
        },

        // Submit match report
        submitMatchReport: function(formData, $submitBtn, $messages) {
            $.ajax({
                url: codClanLeague.ajaxUrl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        CODClanLeague.showMessage($messages, response.data, 'success');
                        $submitBtn.closest('form')[0].reset();
                    } else {
                        CODClanLeague.showMessage($messages, response.data, 'error');
                    }
                },
                error: function() {
                    CODClanLeague.showMessage($messages, codClanLeague.strings.error, 'error');
                },
                complete: function() {
                    $submitBtn.prop('disabled', false).text('Submit Result');
                }
            });
        },

        // Handle clan creation
        handleClanCreation: function(e) {
            e.preventDefault();
            
            var $form = $(this);
            var $submitBtn = $form.find('button[type="submit"]');
            var $messages = $form.find('.cod-form-messages');
            
            $submitBtn.prop('disabled', true);
            
            var formData = {
                action: 'cod_create_clan',
                nonce: codClanLeague.nonce,
                name: $form.find('[name="clan_name"]').val(),
                tag: $form.find('[name="clan_tag"]').val(),
                description: $form.find('[name="clan_description"]').val(),
                logo_url: $form.find('[name="clan_logo"]').val()
            };
            
            $.ajax({
                url: codClanLeague.ajaxUrl,
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        CODClanLeague.showMessage($messages, response.data.message, 'success');
                        $form[0].reset();
                        
                        // Redirect after success if specified
                        if (response.data.redirect) {
                            setTimeout(function() {
                                window.location.href = response.data.redirect;
                            }, 2000);
                        }
                    } else {
                        CODClanLeague.showMessage($messages, response.data, 'error');
                    }
                },
                error: function() {
                    CODClanLeague.showMessage($messages, codClanLeague.strings.error, 'error');
                },
                complete: function() {
                    $submitBtn.prop('disabled', false);
                }
            });
        },

        // Handle team registration
        handleTeamRegistration: function(e) {
            e.preventDefault();
            
            var $btn = $(this);
            var seasonId = $btn.data('season-id');
            var clanId = $btn.data('clan-id');
            
            $btn.prop('disabled', true).text('Registering...');
            
            $.ajax({
                url: codClanLeague.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'cod_register_team',
                    nonce: codClanLeague.nonce,
                    season_id: seasonId,
                    clan_id: clanId
                },
                success: function(response) {
                    if (response.success) {
                        $btn.text('Registered').addClass('cod-btn-success');
                        CODClanLeague.showNotification(response.data, 'success');
                    } else {
                        $btn.prop('disabled', false).text('Register Team');
                        CODClanLeague.showNotification(response.data, 'error');
                    }
                },
                error: function() {
                    $btn.prop('disabled', false).text('Register Team');
                    CODClanLeague.showNotification(codClanLeague.strings.error, 'error');
                }
            });
        },

        // Handle invitation responses
        handleInvitationResponse: function(e) {
            e.preventDefault();
            
            var $btn = $(this);
            var invitationCode = $btn.data('invitation-code');
            var response = $btn.data('response');
            
            $btn.prop('disabled', true);
            
            $.ajax({
                url: codClanLeague.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'cod_respond_invitation',
                    nonce: codClanLeague.nonce,
                    invitation_code: invitationCode,
                    response: response
                },
                success: function(ajaxResponse) {
                    if (ajaxResponse.success) {
                        $btn.closest('.cod-invitation').fadeOut();
                        CODClanLeague.showNotification(ajaxResponse.data, 'success');
                    } else {
                        $btn.prop('disabled', false);
                        CODClanLeague.showNotification(ajaxResponse.data, 'error');
                    }
                },
                error: function() {
                    $btn.prop('disabled', false);
                    CODClanLeague.showNotification(codClanLeague.strings.error, 'error');
                }
            });
        },

        // Handle file uploads
        handleFileUpload: function(e) {
            var file = e.target.files[0];
            var $input = $(this);
            var $preview = $input.siblings('.cod-file-preview');
            
            if (!file) {
                $preview.empty();
                return;
            }
            
            // Validate file
            if (!CODClanLeague.validateFile(file)) {
                $input.val('');
                return;
            }
            
            // Show preview for images
            if (file.type.startsWith('image/')) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    $preview.html('<img src="' + e.target.result + '" style="max-width: 200px; max-height: 200px;">');
                };
                reader.readAsDataURL(file);
            } else {
                $preview.html('<span>File selected: ' + file.name + '</span>');
            }
        },

        // Upload file to server
        uploadFile: function(file, callback) {
            var formData = new FormData();
            formData.append('action', 'cod_upload_match_screenshot');
            formData.append('nonce', codClanLeague.nonce);
            formData.append('screenshot', file);
            
            $.ajax({
                url: codClanLeague.ajaxUrl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: callback,
                error: function() {
                    callback({success: false, data: codClanLeague.strings.error});
                }
            });
        },

        // Validate file
        validateFile: function(file) {
            var allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
            var maxSize = 5 * 1024 * 1024; // 5MB
            
            if (!allowedTypes.includes(file.type)) {
                CODClanLeague.showNotification('Only JPEG, PNG, and GIF images are allowed.', 'error');
                return false;
            }
            
            if (file.size > maxSize) {
                CODClanLeague.showNotification('File size must be less than 5MB.', 'error');
                return false;
            }
            
            return true;
        },

        // Handle confirmation dialogs
        handleConfirmation: function(e) {
            var message = $(this).data('confirm');
            if (!confirm(message)) {
                e.preventDefault();
                return false;
            }
        },

        // Initialize tooltips
        initTooltips: function() {
            $('[data-tooltip]').each(function() {
                $(this).attr('title', $(this).data('tooltip'));
            });
        },

        // Initialize tabs
        initTabs: function() {
            $('.cod-tabs').each(function() {
                var $tabs = $(this);
                var $tabButtons = $tabs.find('.cod-tab-button');
                var $tabPanes = $tabs.find('.cod-tab-pane');
                
                $tabButtons.on('click', function(e) {
                    e.preventDefault();
                    
                    var target = $(this).data('tab');
                    
                    $tabButtons.removeClass('active');
                    $(this).addClass('active');
                    
                    $tabPanes.removeClass('active');
                    $('#' + target).addClass('active');
                });
            });
        },

        // Initialize modals
        initModals: function() {
            $(document).on('click', '[data-modal]', function(e) {
                e.preventDefault();
                var modalId = $(this).data('modal');
                $('#' + modalId).addClass('active');
            });
            
            $(document).on('click', '.cod-modal-close, .cod-modal-overlay', function() {
                $(this).closest('.cod-modal').removeClass('active');
            });
            
            $(document).on('keydown', function(e) {
                if (e.keyCode === 27) { // Escape key
                    $('.cod-modal.active').removeClass('active');
                }
            });
        },

        // Initialize data tables
        initDataTables: function() {
            if ($.fn.DataTable) {
                $('.cod-data-table').DataTable({
                    responsive: true,
                    pageLength: 25,
                    order: [[0, 'asc']],
                    language: {
                        search: 'Search:',
                        lengthMenu: 'Show _MENU_ entries',
                        info: 'Showing _START_ to _END_ of _TOTAL_ entries',
                        paginate: {
                            first: 'First',
                            last: 'Last',
                            next: 'Next',
                            previous: 'Previous'
                        }
                    }
                });
            }
        },

        // Show message in container
        showMessage: function($container, message, type) {
            type = type || 'info';
            var html = '<div class="cod-message cod-' + type + '">' + message + '</div>';
            $container.html(html);
            
            // Auto-hide success messages
            if (type === 'success') {
                setTimeout(function() {
                    $container.fadeOut();
                }, 5000);
            }
        },

        // Show notification (toast-style)
        showNotification: function(message, type) {
            type = type || 'info';
            
            var $notification = $('<div class="cod-notification cod-notification-' + type + '">' + message + '</div>');
            
            $('body').append($notification);
            
            setTimeout(function() {
                $notification.addClass('show');
            }, 100);
            
            setTimeout(function() {
                $notification.removeClass('show');
                setTimeout(function() {
                    $notification.remove();
                }, 300);
            }, 4000);
        },

        // Utility: Debounce function
        debounce: function(func, wait, immediate) {
            var timeout;
            return function() {
                var context = this, args = arguments;
                var later = function() {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                var callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };
        },

        // Utility: Format date
        formatDate: function(date, format) {
            format = format || 'Y-m-d';
            var d = new Date(date);
            
            var formats = {
                'Y': d.getFullYear(),
                'm': ('0' + (d.getMonth() + 1)).slice(-2),
                'd': ('0' + d.getDate()).slice(-2),
                'H': ('0' + d.getHours()).slice(-2),
                'i': ('0' + d.getMinutes()).slice(-2),
                's': ('0' + d.getSeconds()).slice(-2)
            };
            
            return format.replace(/[Ymdis]/g, function(match) {
                return formats[match];
            });
        },

        // Utility: Scroll to element
        scrollTo: function(element, offset) {
            offset = offset || 0;
            var $element = $(element);
            
            if ($element.length) {
                $('html, body').animate({
                    scrollTop: $element.offset().top - offset
                }, 500);
            }
        }
    };

})(jQuery);
