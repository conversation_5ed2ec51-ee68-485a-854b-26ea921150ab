/* Adapted from code in MH Magazine Lite (c) 2018, MH Themes. */

.theme-info-wrap { margin: 25px 40px 0 20px; max-width: 1050px; position: relative; }
.theme-info-wrap img { max-width: 100%; }
.theme-info-wrap .rookie-clearfix:before, .theme-info-wrap .rookie-clearfix:after { content: " "; display: table; }
.theme-info-wrap .rookie-clearfix:after { clear: both; }
.theme-info-wrap .rookie-clearfix { *zoom: 1; }
.theme-info-wrap .rookie-row [class*='rookie-col-']:first-child { margin: 0; }
.theme-info-wrap [class*='rookie-col-'] { float: left; margin-left: 2.5%; overflow: hidden; }
.theme-info-wrap .rookie-col-1-2 { width: 48.75%; }
.theme-info-wrap .rookie-col-1-3 { width: 31.66%; }
.theme-info-wrap .rookie-col-2-3 { width: 65.83%; }
.theme-info-wrap .rookie-col-1-4 { width: 23.12%; }
.theme-info-wrap .rookie-col-3-4 { width: 74.37%; }
.theme-info-wrap .theme-intro { margin: 20px 0; }
.theme-info-wrap h1 { font-size: 40px; font-size: 2.5rem; font-weight: 400; line-height: 1.2; margin-bottom: 0; color: #333333; }
.theme-info-wrap h3 { font-size: 24px; font-size: 1.5rem; line-height: 1.2; }
.theme-info-wrap h4 { font-size: 18px; font-size: 1.125rem; margin: 10px 0; margin: 0.625rem 0; }
.theme-info-wrap .theme-description { font-size: 17px; font-size: 1.0625rem; font-weight: 400; line-height: 1.5; color: #777777; }
.theme-info-wrap .theme-links p strong { font-size: 14px; font-size: 0.875rem; margin-right: 16px; margin-right: 1rem; }
.theme-info-wrap .theme-links p a { font-size: 14px; font-size: 0.875rem; margin-right: 16px; margin-right: 1rem; }
#getting-started { margin: 16px 0; margin: 1rem 0; }
#getting-started .section { margin: 8px 0 30px; margin: 0.5rem 0 1.875rem; }
#getting-started .section .about { font-size: 14px; font-size: 0.875rem; line-height: 1.5; color: #777777; }
#getting-started .section .button { max-width: 100%; }
#getting-started img { margin-top: 16px; margin-top: 1rem; }
.theme-comparison { margin-bottom: 25px; overflow: hidden; }
.theme-comparison table { width: 100%; font-size: 16px; border: 1px solid #ddd; border-collapse: collapse; }
.theme-comparison-header th { width: 33%; background: #555; }
.theme-comparison-header th:first-child { background: #777; }
.theme-comparison-header th:last-child { background: #0085ba; }
.theme-comparison-header h3 { font-size: 18px; color: #fff; margin: 10px 20px; text-align: left; }
.theme-comparison tr:nth-child(2n) { background-color: #f6f6f6; }
.theme-comparison td { padding: 15px 20px; }
.theme-comparison td h3 { font-size: 16px; margin: 0; }
.theme-comparison .dashicons { font-size: 25px; }
.theme-comparison .dashicons-no { color: #e64946; }
.theme-comparison .dashicons-yes { color: #5f9e65; }
.theme-comparison .upgrade-button { display: inline-block; font-size: 12px; font-weight: 600; color: #fff; letter-spacing: 0.5px; padding: 10px; margin: 10px 0; box-shadow: none; border: none; text-align: center; text-decoration: none; text-transform: uppercase; background: #0085ba; }
#theme-author { margin: 16px 0; margin: 1rem 0; }
#theme-author p { font-size: 14px; font-size: 0.875rem; color: #777777; }

@media screen and (max-width: 1000px) {
	.theme-info-wrap .theme-links p strong { display: block; }
	.theme-info-wrap [class*='rookie-col-'] { float: none; width: 100%; margin: 0; }
	.theme-info-wrap .theme-screenshot { margin-bottom: 20px; }
}