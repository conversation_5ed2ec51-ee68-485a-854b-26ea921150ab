<?php
/**
 * Custom post types and taxonomies
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class COD_Clan_League_Post_Types {
    
    /**
     * Initialize post types
     */
    public function init() {
        add_action('init', array($this, 'register_post_types'));
        add_action('init', array($this, 'register_taxonomies'));
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
        add_action('save_post', array($this, 'save_meta_boxes'));
    }
    
    /**
     * Register custom post types
     */
    public function register_post_types() {
        // Clan post type
        $clan_args = array(
            'labels' => array(
                'name' => __('Clans', 'cod-clan-league'),
                'singular_name' => __('Clan', 'cod-clan-league'),
                'menu_name' => __('Clans', 'cod-clan-league'),
                'add_new' => __('Add New Clan', 'cod-clan-league'),
                'add_new_item' => __('Add New Clan', 'cod-clan-league'),
                'edit_item' => __('Edit Clan', 'cod-clan-league'),
                'new_item' => __('New Clan', 'cod-clan-league'),
                'view_item' => __('View Clan', 'cod-clan-league'),
                'search_items' => __('Search Clans', 'cod-clan-league'),
                'not_found' => __('No clans found', 'cod-clan-league'),
                'not_found_in_trash' => __('No clans found in trash', 'cod-clan-league'),
            ),
            'public' => true,
            'publicly_queryable' => true,
            'show_ui' => true,
            'show_in_menu' => 'cod-clan-league',
            'query_var' => true,
            'rewrite' => array('slug' => 'clan'),
            'capability_type' => 'post',
            'has_archive' => true,
            'hierarchical' => false,
            'menu_position' => null,
            'supports' => array('title', 'editor', 'thumbnail', 'custom-fields'),
            'show_in_rest' => true,
        );
        register_post_type('cod_clan', $clan_args);
        
        // Season post type
        $season_args = array(
            'labels' => array(
                'name' => __('Seasons', 'cod-clan-league'),
                'singular_name' => __('Season', 'cod-clan-league'),
                'menu_name' => __('Seasons', 'cod-clan-league'),
                'add_new' => __('Add New Season', 'cod-clan-league'),
                'add_new_item' => __('Add New Season', 'cod-clan-league'),
                'edit_item' => __('Edit Season', 'cod-clan-league'),
                'new_item' => __('New Season', 'cod-clan-league'),
                'view_item' => __('View Season', 'cod-clan-league'),
                'search_items' => __('Search Seasons', 'cod-clan-league'),
                'not_found' => __('No seasons found', 'cod-clan-league'),
                'not_found_in_trash' => __('No seasons found in trash', 'cod-clan-league'),
            ),
            'public' => true,
            'publicly_queryable' => true,
            'show_ui' => true,
            'show_in_menu' => 'cod-clan-league',
            'query_var' => true,
            'rewrite' => array('slug' => 'season'),
            'capability_type' => 'post',
            'has_archive' => true,
            'hierarchical' => false,
            'menu_position' => null,
            'supports' => array('title', 'editor', 'custom-fields'),
            'show_in_rest' => true,
        );
        register_post_type('cod_season', $season_args);
        
        // Match post type
        $match_args = array(
            'labels' => array(
                'name' => __('Matches', 'cod-clan-league'),
                'singular_name' => __('Match', 'cod-clan-league'),
                'menu_name' => __('Matches', 'cod-clan-league'),
                'add_new' => __('Add New Match', 'cod-clan-league'),
                'add_new_item' => __('Add New Match', 'cod-clan-league'),
                'edit_item' => __('Edit Match', 'cod-clan-league'),
                'new_item' => __('New Match', 'cod-clan-league'),
                'view_item' => __('View Match', 'cod-clan-league'),
                'search_items' => __('Search Matches', 'cod-clan-league'),
                'not_found' => __('No matches found', 'cod-clan-league'),
                'not_found_in_trash' => __('No matches found in trash', 'cod-clan-league'),
            ),
            'public' => true,
            'publicly_queryable' => true,
            'show_ui' => true,
            'show_in_menu' => 'cod-clan-league',
            'query_var' => true,
            'rewrite' => array('slug' => 'match'),
            'capability_type' => 'post',
            'has_archive' => true,
            'hierarchical' => false,
            'menu_position' => null,
            'supports' => array('title', 'editor', 'custom-fields'),
            'show_in_rest' => true,
        );
        register_post_type('cod_match', $match_args);
    }
    
    /**
     * Register taxonomies
     */
    public function register_taxonomies() {
        // Match status taxonomy
        $match_status_args = array(
            'labels' => array(
                'name' => __('Match Status', 'cod-clan-league'),
                'singular_name' => __('Match Status', 'cod-clan-league'),
                'menu_name' => __('Match Status', 'cod-clan-league'),
            ),
            'hierarchical' => false,
            'public' => true,
            'show_ui' => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => true,
            'show_tagcloud' => false,
            'show_in_rest' => true,
        );
        register_taxonomy('cod_match_status', array('cod_match'), $match_status_args);
        
        // Season status taxonomy
        $season_status_args = array(
            'labels' => array(
                'name' => __('Season Status', 'cod-clan-league'),
                'singular_name' => __('Season Status', 'cod-clan-league'),
                'menu_name' => __('Season Status', 'cod-clan-league'),
            ),
            'hierarchical' => false,
            'public' => true,
            'show_ui' => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => true,
            'show_tagcloud' => false,
            'show_in_rest' => true,
        );
        register_taxonomy('cod_season_status', array('cod_season'), $season_status_args);
        
        // Game mode taxonomy
        $game_mode_args = array(
            'labels' => array(
                'name' => __('Game Modes', 'cod-clan-league'),
                'singular_name' => __('Game Mode', 'cod-clan-league'),
                'menu_name' => __('Game Modes', 'cod-clan-league'),
            ),
            'hierarchical' => true,
            'public' => true,
            'show_ui' => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => true,
            'show_tagcloud' => true,
            'show_in_rest' => true,
        );
        register_taxonomy('cod_game_mode', array('cod_match', 'cod_season'), $game_mode_args);
    }
    
    /**
     * Add meta boxes
     */
    public function add_meta_boxes() {
        // Clan meta boxes
        add_meta_box(
            'cod_clan_details',
            __('Clan Details', 'cod-clan-league'),
            array($this, 'clan_details_meta_box'),
            'cod_clan',
            'normal',
            'high'
        );
        
        // Season meta boxes
        add_meta_box(
            'cod_season_details',
            __('Season Details', 'cod-clan-league'),
            array($this, 'season_details_meta_box'),
            'cod_season',
            'normal',
            'high'
        );
        
        // Match meta boxes
        add_meta_box(
            'cod_match_details',
            __('Match Details', 'cod-clan-league'),
            array($this, 'match_details_meta_box'),
            'cod_match',
            'normal',
            'high'
        );
    }
    
    /**
     * Clan details meta box
     */
    public function clan_details_meta_box($post) {
        wp_nonce_field('cod_clan_meta_box', 'cod_clan_meta_box_nonce');
        
        $clan_tag = get_post_meta($post->ID, '_clan_tag', true);
        $clan_captain = get_post_meta($post->ID, '_clan_captain', true);
        $clan_status = get_post_meta($post->ID, '_clan_status', true);
        $clan_logo = get_post_meta($post->ID, '_clan_logo', true);
        
        echo '<table class="form-table">';
        echo '<tr>';
        echo '<th><label for="clan_tag">' . __('Clan Tag', 'cod-clan-league') . '</label></th>';
        echo '<td><input type="text" id="clan_tag" name="clan_tag" value="' . esc_attr($clan_tag) . '" maxlength="10" /></td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th><label for="clan_captain">' . __('Captain', 'cod-clan-league') . '</label></th>';
        echo '<td>';
        wp_dropdown_users(array(
            'name' => 'clan_captain',
            'selected' => $clan_captain,
            'show_option_none' => __('Select Captain', 'cod-clan-league'),
        ));
        echo '</td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th><label for="clan_status">' . __('Status', 'cod-clan-league') . '</label></th>';
        echo '<td>';
        echo '<select id="clan_status" name="clan_status">';
        echo '<option value="pending"' . selected($clan_status, 'pending', false) . '>' . __('Pending', 'cod-clan-league') . '</option>';
        echo '<option value="active"' . selected($clan_status, 'active', false) . '>' . __('Active', 'cod-clan-league') . '</option>';
        echo '<option value="inactive"' . selected($clan_status, 'inactive', false) . '>' . __('Inactive', 'cod-clan-league') . '</option>';
        echo '</select>';
        echo '</td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th><label for="clan_logo">' . __('Logo URL', 'cod-clan-league') . '</label></th>';
        echo '<td><input type="url" id="clan_logo" name="clan_logo" value="' . esc_attr($clan_logo) . '" class="regular-text" /></td>';
        echo '</tr>';
        echo '</table>';
    }
    
    /**
     * Season details meta box
     */
    public function season_details_meta_box($post) {
        wp_nonce_field('cod_season_meta_box', 'cod_season_meta_box_nonce');
        
        $start_date = get_post_meta($post->ID, '_season_start_date', true);
        $end_date = get_post_meta($post->ID, '_season_end_date', true);
        $max_teams = get_post_meta($post->ID, '_season_max_teams', true) ?: 16;
        $status = get_post_meta($post->ID, '_season_status', true);
        
        echo '<table class="form-table">';
        echo '<tr>';
        echo '<th><label for="season_start_date">' . __('Start Date', 'cod-clan-league') . '</label></th>';
        echo '<td><input type="date" id="season_start_date" name="season_start_date" value="' . esc_attr($start_date) . '" /></td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th><label for="season_end_date">' . __('End Date', 'cod-clan-league') . '</label></th>';
        echo '<td><input type="date" id="season_end_date" name="season_end_date" value="' . esc_attr($end_date) . '" /></td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th><label for="season_max_teams">' . __('Max Teams', 'cod-clan-league') . '</label></th>';
        echo '<td><input type="number" id="season_max_teams" name="season_max_teams" value="' . esc_attr($max_teams) . '" min="2" max="64" /></td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th><label for="season_status">' . __('Status', 'cod-clan-league') . '</label></th>';
        echo '<td>';
        echo '<select id="season_status" name="season_status">';
        echo '<option value="upcoming"' . selected($status, 'upcoming', false) . '>' . __('Upcoming', 'cod-clan-league') . '</option>';
        echo '<option value="active"' . selected($status, 'active', false) . '>' . __('Active', 'cod-clan-league') . '</option>';
        echo '<option value="completed"' . selected($status, 'completed', false) . '>' . __('Completed', 'cod-clan-league') . '</option>';
        echo '</select>';
        echo '</td>';
        echo '</tr>';
        echo '</table>';
    }
    
    /**
     * Match details meta box
     */
    public function match_details_meta_box($post) {
        wp_nonce_field('cod_match_meta_box', 'cod_match_meta_box_nonce');
        
        $season_id = get_post_meta($post->ID, '_match_season_id', true);
        $home_team = get_post_meta($post->ID, '_match_home_team', true);
        $away_team = get_post_meta($post->ID, '_match_away_team', true);
        $scheduled_date = get_post_meta($post->ID, '_match_scheduled_date', true);
        $home_score = get_post_meta($post->ID, '_match_home_score', true);
        $away_score = get_post_meta($post->ID, '_match_away_score', true);
        $status = get_post_meta($post->ID, '_match_status', true);
        
        echo '<table class="form-table">';
        echo '<tr>';
        echo '<th><label for="match_season_id">' . __('Season', 'cod-clan-league') . '</label></th>';
        echo '<td>';
        $seasons = get_posts(array('post_type' => 'cod_season', 'numberposts' => -1));
        echo '<select id="match_season_id" name="match_season_id">';
        echo '<option value="">' . __('Select Season', 'cod-clan-league') . '</option>';
        foreach ($seasons as $season) {
            echo '<option value="' . $season->ID . '"' . selected($season_id, $season->ID, false) . '>' . esc_html($season->post_title) . '</option>';
        }
        echo '</select>';
        echo '</td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th><label for="match_scheduled_date">' . __('Scheduled Date', 'cod-clan-league') . '</label></th>';
        echo '<td><input type="datetime-local" id="match_scheduled_date" name="match_scheduled_date" value="' . esc_attr($scheduled_date) . '" /></td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th><label for="match_home_score">' . __('Home Score', 'cod-clan-league') . '</label></th>';
        echo '<td><input type="number" id="match_home_score" name="match_home_score" value="' . esc_attr($home_score) . '" min="0" /></td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th><label for="match_away_score">' . __('Away Score', 'cod-clan-league') . '</label></th>';
        echo '<td><input type="number" id="match_away_score" name="match_away_score" value="' . esc_attr($away_score) . '" min="0" /></td>';
        echo '</tr>';
        echo '</table>';
    }
    
    /**
     * Save meta box data
     */
    public function save_meta_boxes($post_id) {
        // Check if user has permission to edit
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        
        // Save clan meta
        if (isset($_POST['cod_clan_meta_box_nonce']) && wp_verify_nonce($_POST['cod_clan_meta_box_nonce'], 'cod_clan_meta_box')) {
            if (isset($_POST['clan_tag'])) {
                update_post_meta($post_id, '_clan_tag', sanitize_text_field($_POST['clan_tag']));
            }
            if (isset($_POST['clan_captain'])) {
                update_post_meta($post_id, '_clan_captain', intval($_POST['clan_captain']));
            }
            if (isset($_POST['clan_status'])) {
                update_post_meta($post_id, '_clan_status', sanitize_text_field($_POST['clan_status']));
            }
            if (isset($_POST['clan_logo'])) {
                update_post_meta($post_id, '_clan_logo', esc_url_raw($_POST['clan_logo']));
            }
        }
        
        // Save season meta
        if (isset($_POST['cod_season_meta_box_nonce']) && wp_verify_nonce($_POST['cod_season_meta_box_nonce'], 'cod_season_meta_box')) {
            if (isset($_POST['season_start_date'])) {
                update_post_meta($post_id, '_season_start_date', sanitize_text_field($_POST['season_start_date']));
            }
            if (isset($_POST['season_end_date'])) {
                update_post_meta($post_id, '_season_end_date', sanitize_text_field($_POST['season_end_date']));
            }
            if (isset($_POST['season_max_teams'])) {
                update_post_meta($post_id, '_season_max_teams', intval($_POST['season_max_teams']));
            }
            if (isset($_POST['season_status'])) {
                update_post_meta($post_id, '_season_status', sanitize_text_field($_POST['season_status']));
            }
        }
        
        // Save match meta
        if (isset($_POST['cod_match_meta_box_nonce']) && wp_verify_nonce($_POST['cod_match_meta_box_nonce'], 'cod_match_meta_box')) {
            if (isset($_POST['match_season_id'])) {
                update_post_meta($post_id, '_match_season_id', intval($_POST['match_season_id']));
            }
            if (isset($_POST['match_scheduled_date'])) {
                update_post_meta($post_id, '_match_scheduled_date', sanitize_text_field($_POST['match_scheduled_date']));
            }
            if (isset($_POST['match_home_score'])) {
                update_post_meta($post_id, '_match_home_score', intval($_POST['match_home_score']));
            }
            if (isset($_POST['match_away_score'])) {
                update_post_meta($post_id, '_match_away_score', intval($_POST['match_away_score']));
            }
        }
    }
}
