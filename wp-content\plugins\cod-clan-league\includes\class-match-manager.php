<?php
/**
 * Match management and reporting functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class COD_Clan_League_Match_Manager {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('wp_ajax_cod_report_match_result', array($this, 'ajax_report_match_result'));
        add_action('wp_ajax_cod_approve_match_result', array($this, 'ajax_approve_match_result'));
        add_action('wp_ajax_cod_upload_match_screenshot', array($this, 'ajax_upload_match_screenshot'));
        add_action('wp_ajax_cod_override_match_result', array($this, 'ajax_override_match_result'));
        
        add_action('init', array($this, 'handle_file_uploads'));
    }
    
    /**
     * Report match result
     */
    public function report_match_result($match_id, $data, $reported_by = null) {
        global $wpdb;
        
        if (!$reported_by) {
            $reported_by = get_current_user_id();
        }
        
        // Validate permissions
        $user_roles = new COD_Clan_League_User_Roles();
        if (!$user_roles->user_can_report_match($reported_by, $match_id) && !current_user_can('manage_cod_matches')) {
            return new WP_Error('no_permission', __('You do not have permission to report this match.', 'cod-clan-league'));
        }
        
        // Get match
        $match = $this->get_match($match_id);
        if (!$match) {
            return new WP_Error('match_not_found', __('Match not found.', 'cod-clan-league'));
        }
        
        if ($match->status !== 'scheduled') {
            return new WP_Error('match_not_scheduled', __('Match is not in scheduled status.', 'cod-clan-league'));
        }
        
        // Validate scores
        $home_score = intval($data['home_score'] ?? 0);
        $away_score = intval($data['away_score'] ?? 0);
        
        if ($home_score < 0 || $away_score < 0) {
            return new WP_Error('invalid_scores', __('Scores cannot be negative.', 'cod-clan-league'));
        }
        
        if ($home_score === $away_score) {
            $options = get_option('cod_clan_league_options', array());
            if (!($options['allow_draws'] ?? false)) {
                return new WP_Error('draws_not_allowed', __('Draws are not allowed in this league.', 'cod-clan-league'));
            }
        }
        
        // Determine winner
        $winner_team_id = null;
        if ($home_score > $away_score) {
            $winner_team_id = $match->home_team_id;
        } elseif ($away_score > $home_score) {
            $winner_team_id = $match->away_team_id;
        }
        
        // Prepare update data
        $matches_table = $wpdb->prefix . 'cod_matches';
        $update_data = array(
            'home_score' => $home_score,
            'away_score' => $away_score,
            'winner_team_id' => $winner_team_id,
            'notes' => sanitize_textarea_field($data['notes'] ?? ''),
            'reported_by' => $reported_by,
            'completed_date' => current_time('mysql'),
        );
        
        // Check if auto-approval is enabled
        $options = get_option('cod_clan_league_options', array());
        $auto_approve = $options['auto_approve_results'] ?? false;
        
        if ($auto_approve || current_user_can('approve_cod_results')) {
            $update_data['status'] = 'completed';
            $update_data['approved_by'] = $reported_by;
        } else {
            $update_data['status'] = 'pending_approval';
        }
        
        // Handle screenshot upload
        if (!empty($data['screenshot_url'])) {
            $update_data['screenshot_url'] = esc_url_raw($data['screenshot_url']);
        }
        
        // Update match
        $result = $wpdb->update(
            $matches_table,
            $update_data,
            array('id' => $match_id)
        );
        
        if ($result === false) {
            return new WP_Error('db_error', __('Failed to update match result.', 'cod-clan-league'));
        }
        
        // Update league table if match is completed
        if ($update_data['status'] === 'completed') {
            $league_manager = new COD_Clan_League_League_Manager();
            $league_manager->update_league_table($match->season_id);
        }
        
        do_action('cod_match_result_reported', $match_id, $data, $reported_by);
        
        return true;
    }
    
    /**
     * Approve match result
     */
    public function approve_match_result($match_id, $approved = true, $approved_by = null) {
        global $wpdb;
        
        if (!$approved_by) {
            $approved_by = get_current_user_id();
        }
        
        if (!current_user_can('approve_cod_results')) {
            return new WP_Error('no_permission', __('You do not have permission to approve match results.', 'cod-clan-league'));
        }
        
        $match = $this->get_match($match_id);
        if (!$match) {
            return new WP_Error('match_not_found', __('Match not found.', 'cod-clan-league'));
        }
        
        if ($match->status !== 'pending_approval') {
            return new WP_Error('not_pending', __('Match is not pending approval.', 'cod-clan-league'));
        }
        
        $matches_table = $wpdb->prefix . 'cod_matches';
        $new_status = $approved ? 'completed' : 'scheduled';
        
        $update_data = array(
            'status' => $new_status,
            'approved_by' => $approved_by,
        );
        
        // If not approved, clear the result data
        if (!$approved) {
            $update_data['home_score'] = null;
            $update_data['away_score'] = null;
            $update_data['winner_team_id'] = null;
            $update_data['notes'] = '';
            $update_data['screenshot_url'] = '';
            $update_data['reported_by'] = null;
            $update_data['completed_date'] = null;
        }
        
        $result = $wpdb->update(
            $matches_table,
            $update_data,
            array('id' => $match_id)
        );
        
        if ($result === false) {
            return new WP_Error('db_error', __('Failed to update match status.', 'cod-clan-league'));
        }
        
        // Update league table if approved
        if ($approved) {
            $league_manager = new COD_Clan_League_League_Manager();
            $league_manager->update_league_table($match->season_id);
        }
        
        do_action('cod_match_result_approved', $match_id, $approved, $approved_by);
        
        return true;
    }
    
    /**
     * Override match result (admin only)
     */
    public function override_match_result($match_id, $data, $overridden_by = null) {
        global $wpdb;
        
        if (!$overridden_by) {
            $overridden_by = get_current_user_id();
        }
        
        if (!current_user_can('override_cod_results')) {
            return new WP_Error('no_permission', __('You do not have permission to override match results.', 'cod-clan-league'));
        }
        
        $match = $this->get_match($match_id);
        if (!$match) {
            return new WP_Error('match_not_found', __('Match not found.', 'cod-clan-league'));
        }
        
        // Validate scores
        $home_score = intval($data['home_score'] ?? 0);
        $away_score = intval($data['away_score'] ?? 0);
        
        if ($home_score < 0 || $away_score < 0) {
            return new WP_Error('invalid_scores', __('Scores cannot be negative.', 'cod-clan-league'));
        }
        
        // Determine winner
        $winner_team_id = null;
        if ($home_score > $away_score) {
            $winner_team_id = $match->home_team_id;
        } elseif ($away_score > $home_score) {
            $winner_team_id = $match->away_team_id;
        }
        
        $matches_table = $wpdb->prefix . 'cod_matches';
        $update_data = array(
            'home_score' => $home_score,
            'away_score' => $away_score,
            'winner_team_id' => $winner_team_id,
            'notes' => sanitize_textarea_field($data['notes'] ?? ''),
            'status' => 'completed',
            'approved_by' => $overridden_by,
            'completed_date' => current_time('mysql'),
        );
        
        if (!empty($data['screenshot_url'])) {
            $update_data['screenshot_url'] = esc_url_raw($data['screenshot_url']);
        }
        
        $result = $wpdb->update(
            $matches_table,
            $update_data,
            array('id' => $match_id)
        );
        
        if ($result === false) {
            return new WP_Error('db_error', __('Failed to override match result.', 'cod-clan-league'));
        }
        
        // Update league table
        $league_manager = new COD_Clan_League_League_Manager();
        $league_manager->update_league_table($match->season_id);
        
        do_action('cod_match_result_overridden', $match_id, $data, $overridden_by);
        
        return true;
    }
    
    /**
     * Upload match screenshot
     */
    public function upload_match_screenshot($match_id, $file) {
        if (!current_user_can('upload_files')) {
            return new WP_Error('no_permission', __('You do not have permission to upload files.', 'cod-clan-league'));
        }
        
        // Validate file type
        $allowed_types = array('image/jpeg', 'image/png', 'image/gif');
        if (!in_array($file['type'], $allowed_types)) {
            return new WP_Error('invalid_file_type', __('Only JPEG, PNG, and GIF images are allowed.', 'cod-clan-league'));
        }
        
        // Validate file size (5MB max)
        $max_size = 5 * 1024 * 1024; // 5MB
        if ($file['size'] > $max_size) {
            return new WP_Error('file_too_large', __('File size must be less than 5MB.', 'cod-clan-league'));
        }
        
        // Handle upload
        if (!function_exists('wp_handle_upload')) {
            require_once(ABSPATH . 'wp-admin/includes/file.php');
        }
        
        $upload_overrides = array(
            'test_form' => false,
            'unique_filename_callback' => function($dir, $name, $ext) use ($match_id) {
                return 'match_' . $match_id . '_' . time() . $ext;
            }
        );
        
        $uploaded_file = wp_handle_upload($file, $upload_overrides);
        
        if (isset($uploaded_file['error'])) {
            return new WP_Error('upload_error', $uploaded_file['error']);
        }
        
        return $uploaded_file['url'];
    }
    
    /**
     * Get match by ID
     */
    public function get_match($match_id) {
        global $wpdb;
        
        $matches_table = $wpdb->prefix . 'cod_matches';
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $matches_table WHERE id = %d",
            $match_id
        ));
    }
    
    /**
     * Get matches for season
     */
    public function get_season_matches($season_id, $status = null) {
        global $wpdb;
        
        $matches_table = $wpdb->prefix . 'cod_matches';
        $season_teams_table = $wpdb->prefix . 'cod_season_teams';
        $clans_table = $wpdb->prefix . 'cod_clans';
        
        $query = "SELECT m.*, 
                         ht.clan_id as home_clan_id, hc.name as home_clan_name, hc.tag as home_clan_tag,
                         at.clan_id as away_clan_id, ac.name as away_clan_name, ac.tag as away_clan_tag
                  FROM $matches_table m
                  INNER JOIN $season_teams_table ht ON m.home_team_id = ht.id
                  INNER JOIN $season_teams_table at ON m.away_team_id = at.id
                  INNER JOIN $clans_table hc ON ht.clan_id = hc.id
                  INNER JOIN $clans_table ac ON at.clan_id = ac.id
                  WHERE m.season_id = %d";
        
        $params = array($season_id);
        
        if ($status) {
            $query .= " AND m.status = %s";
            $params[] = $status;
        }
        
        $query .= " ORDER BY m.scheduled_date ASC";
        
        return $wpdb->get_results($wpdb->prepare($query, $params));
    }
    
    /**
     * Get matches for team
     */
    public function get_team_matches($team_id, $status = null) {
        global $wpdb;
        
        $matches_table = $wpdb->prefix . 'cod_matches';
        
        $query = "SELECT * FROM $matches_table 
                  WHERE (home_team_id = %d OR away_team_id = %d)";
        
        $params = array($team_id, $team_id);
        
        if ($status) {
            $query .= " AND status = %s";
            $params[] = $status;
        }
        
        $query .= " ORDER BY scheduled_date ASC";
        
        return $wpdb->get_results($wpdb->prepare($query, $params));
    }
    
    /**
     * Handle file uploads
     */
    public function handle_file_uploads() {
        // This method can be used to handle custom file upload endpoints
        // For now, we'll use WordPress's built-in upload handling
    }

    /**
     * AJAX: Report match result
     */
    public function ajax_report_match_result() {
        check_ajax_referer('cod_clan_league_nonce', 'nonce');

        if (!is_user_logged_in()) {
            wp_send_json_error(__('You must be logged in.', 'cod-clan-league'));
        }

        $match_id = intval($_POST['match_id'] ?? 0);
        $data = array(
            'home_score' => intval($_POST['home_score'] ?? 0),
            'away_score' => intval($_POST['away_score'] ?? 0),
            'notes' => sanitize_textarea_field($_POST['notes'] ?? ''),
            'screenshot_url' => esc_url_raw($_POST['screenshot_url'] ?? ''),
        );

        $result = $this->report_match_result($match_id, $data);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(__('Match result reported successfully!', 'cod-clan-league'));
        }
    }

    /**
     * AJAX: Approve match result
     */
    public function ajax_approve_match_result() {
        check_ajax_referer('cod_clan_league_admin_nonce', 'nonce');

        if (!current_user_can('approve_cod_results')) {
            wp_send_json_error(__('You do not have permission to approve match results.', 'cod-clan-league'));
        }

        $match_id = intval($_POST['match_id'] ?? 0);
        $approved = $_POST['approved'] === 'true';

        $result = $this->approve_match_result($match_id, $approved);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            $message = $approved
                ? __('Match result approved!', 'cod-clan-league')
                : __('Match result rejected.', 'cod-clan-league');
            wp_send_json_success($message);
        }
    }

    /**
     * AJAX: Upload match screenshot
     */
    public function ajax_upload_match_screenshot() {
        check_ajax_referer('cod_clan_league_nonce', 'nonce');

        if (!is_user_logged_in()) {
            wp_send_json_error(__('You must be logged in.', 'cod-clan-league'));
        }

        if (empty($_FILES['screenshot'])) {
            wp_send_json_error(__('No file uploaded.', 'cod-clan-league'));
        }

        $match_id = intval($_POST['match_id'] ?? 0);
        $file = $_FILES['screenshot'];

        $result = $this->upload_match_screenshot($match_id, $file);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(array(
                'url' => $result,
                'message' => __('Screenshot uploaded successfully!', 'cod-clan-league'),
            ));
        }
    }

    /**
     * AJAX: Override match result
     */
    public function ajax_override_match_result() {
        check_ajax_referer('cod_clan_league_admin_nonce', 'nonce');

        if (!current_user_can('override_cod_results')) {
            wp_send_json_error(__('You do not have permission to override match results.', 'cod-clan-league'));
        }

        $match_id = intval($_POST['match_id'] ?? 0);
        $data = array(
            'home_score' => intval($_POST['home_score'] ?? 0),
            'away_score' => intval($_POST['away_score'] ?? 0),
            'notes' => sanitize_textarea_field($_POST['notes'] ?? ''),
            'screenshot_url' => esc_url_raw($_POST['screenshot_url'] ?? ''),
        );

        $result = $this->override_match_result($match_id, $data);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(__('Match result overridden successfully!', 'cod-clan-league'));
        }
    }
}
