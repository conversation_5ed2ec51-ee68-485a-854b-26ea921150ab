<?php
/**
 * Chat management functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Site_Chat_Manager {
    
    /**
     * Constructor
     */
    public function __construct() {
        // AJAX handlers for logged-in users
        add_action('wp_ajax_send_chat_message', array($this, 'ajax_send_message'));
        add_action('wp_ajax_get_chat_messages', array($this, 'ajax_get_messages'));
        add_action('wp_ajax_get_online_users', array($this, 'ajax_get_online_users'));
        add_action('wp_ajax_update_user_status', array($this, 'ajax_update_user_status'));
        add_action('wp_ajax_delete_chat_message', array($this, 'ajax_delete_message'));
        add_action('wp_ajax_edit_chat_message', array($this, 'ajax_edit_message'));
        add_action('wp_ajax_react_to_message', array($this, 'ajax_react_to_message'));
        add_action('wp_ajax_block_user', array($this, 'ajax_block_user'));
        add_action('wp_ajax_upload_chat_file', array($this, 'ajax_upload_file'));
        
        // Cleanup scheduled events
        add_action('site_chat_cleanup_messages', array($this, 'cleanup_old_messages'));
        add_action('site_chat_cleanup_offline_users', array($this, 'cleanup_offline_users'));
    }
    
    /**
     * Initialize chat manager
     */
    public function init() {
        // Schedule cleanup events
        if (!wp_next_scheduled('site_chat_cleanup_messages')) {
            wp_schedule_event(time(), 'daily', 'site_chat_cleanup_messages');
        }
        
        if (!wp_next_scheduled('site_chat_cleanup_offline_users')) {
            wp_schedule_event(time(), 'hourly', 'site_chat_cleanup_offline_users');
        }
    }
    
    /**
     * Send a chat message
     */
    public function send_message($room_id, $user_id, $message, $message_type = 'text', $file_data = null) {
        global $wpdb;
        
        // Validate user permissions
        if (!$this->can_user_send_message($user_id, $room_id)) {
            return new WP_Error('permission_denied', __('You do not have permission to send messages in this room.', 'site-chat-system'));
        }
        
        // Validate message content
        $options = get_option('site_chat_options', array());
        $max_length = $options['max_message_length'] ?? 500;
        
        if (strlen($message) > $max_length) {
            return new WP_Error('message_too_long', sprintf(__('Message must be %d characters or less.', 'site-chat-system'), $max_length));
        }
        
        // Sanitize message
        $message = wp_kses_post($message);
        
        // Prepare message data
        $message_data = array(
            'room_id' => $room_id,
            'user_id' => $user_id,
            'message' => $message,
            'message_type' => $message_type,
            'created_date' => current_time('mysql')
        );
        
        // Add file data if present
        if ($file_data) {
            $message_data['file_url'] = $file_data['url'];
            $message_data['file_name'] = $file_data['name'];
            $message_data['file_size'] = $file_data['size'];
        }
        
        $messages_table = $wpdb->prefix . 'chat_messages';
        $result = $wpdb->insert($messages_table, $message_data);
        
        if ($result === false) {
            return new WP_Error('db_error', __('Failed to send message.', 'site-chat-system'));
        }
        
        $message_id = $wpdb->insert_id;
        
        // Update user online status
        $this->update_user_online_status($user_id);
        
        do_action('site_chat_message_sent', $message_id, $room_id, $user_id);
        
        return $message_id;
    }
    
    /**
     * Get chat messages for a room
     */
    public function get_messages($room_id, $limit = 50, $offset = 0, $since_id = 0) {
        global $wpdb;
        
        $messages_table = $wpdb->prefix . 'chat_messages';
        $users_table = $wpdb->users;
        
        $query = "SELECT m.*, u.display_name, u.user_email
                  FROM $messages_table m
                  INNER JOIN $users_table u ON m.user_id = u.ID
                  WHERE m.room_id = %d";
        
        $params = array($room_id);
        
        if ($since_id > 0) {
            $query .= " AND m.id > %d";
            $params[] = $since_id;
        }
        
        $query .= " ORDER BY m.created_date DESC LIMIT %d OFFSET %d";
        $params[] = $limit;
        $params[] = $offset;
        
        $messages = $wpdb->get_results($wpdb->prepare($query, $params));
        
        // Reverse order to show oldest first
        $messages = array_reverse($messages);
        
        // Add avatar URLs and format data
        foreach ($messages as &$message) {
            $message->avatar_url = get_avatar_url($message->user_id, array('size' => 32));
            $message->formatted_date = $this->format_message_date($message->created_date);
            $message->reactions = $this->get_message_reactions($message->id);
        }
        
        return $messages;
    }
    
    /**
     * Get online users
     */
    public function get_online_users() {
        $database = new Site_Chat_Database();
        return $database->get_online_users();
    }
    
    /**
     * Update user online status
     */
    public function update_user_online_status($user_id, $status = 'online') {
        $database = new Site_Chat_Database();
        $database->update_user_online_status($user_id, $status);
    }
    
    /**
     * Delete a message
     */
    public function delete_message($message_id, $user_id) {
        global $wpdb;
        
        $messages_table = $wpdb->prefix . 'chat_messages';
        
        // Check if user can delete this message
        $message = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $messages_table WHERE id = %d",
            $message_id
        ));
        
        if (!$message) {
            return new WP_Error('message_not_found', __('Message not found.', 'site-chat-system'));
        }
        
        // Users can delete their own messages, admins can delete any message
        if ($message->user_id != $user_id && !current_user_can('manage_options')) {
            return new WP_Error('permission_denied', __('You do not have permission to delete this message.', 'site-chat-system'));
        }
        
        $result = $wpdb->delete($messages_table, array('id' => $message_id));
        
        if ($result === false) {
            return new WP_Error('db_error', __('Failed to delete message.', 'site-chat-system'));
        }
        
        do_action('site_chat_message_deleted', $message_id, $user_id);
        
        return true;
    }
    
    /**
     * Edit a message
     */
    public function edit_message($message_id, $user_id, $new_content) {
        global $wpdb;
        
        $messages_table = $wpdb->prefix . 'chat_messages';
        
        // Check if user can edit this message
        $message = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $messages_table WHERE id = %d",
            $message_id
        ));
        
        if (!$message) {
            return new WP_Error('message_not_found', __('Message not found.', 'site-chat-system'));
        }
        
        // Only message author can edit
        if ($message->user_id != $user_id) {
            return new WP_Error('permission_denied', __('You can only edit your own messages.', 'site-chat-system'));
        }
        
        // Validate content
        $options = get_option('site_chat_options', array());
        $max_length = $options['max_message_length'] ?? 500;
        
        if (strlen($new_content) > $max_length) {
            return new WP_Error('message_too_long', sprintf(__('Message must be %d characters or less.', 'site-chat-system'), $max_length));
        }
        
        $new_content = wp_kses_post($new_content);
        
        $result = $wpdb->update(
            $messages_table,
            array(
                'message' => $new_content,
                'is_edited' => 1,
                'edited_date' => current_time('mysql')
            ),
            array('id' => $message_id)
        );
        
        if ($result === false) {
            return new WP_Error('db_error', __('Failed to edit message.', 'site-chat-system'));
        }
        
        do_action('site_chat_message_edited', $message_id, $user_id);
        
        return true;
    }
    
    /**
     * Add reaction to message
     */
    public function react_to_message($message_id, $user_id, $reaction) {
        global $wpdb;
        
        $reactions_table = $wpdb->prefix . 'chat_message_reactions';
        
        // Check if user already reacted with this emoji
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $reactions_table WHERE message_id = %d AND user_id = %d AND reaction = %s",
            $message_id, $user_id, $reaction
        ));
        
        if ($existing) {
            // Remove existing reaction
            $wpdb->delete($reactions_table, array('id' => $existing));
            return 'removed';
        } else {
            // Add new reaction
            $result = $wpdb->insert(
                $reactions_table,
                array(
                    'message_id' => $message_id,
                    'user_id' => $user_id,
                    'reaction' => $reaction,
                    'created_date' => current_time('mysql')
                )
            );
            
            return $result !== false ? 'added' : false;
        }
    }
    
    /**
     * Get message reactions
     */
    public function get_message_reactions($message_id) {
        global $wpdb;
        
        $reactions_table = $wpdb->prefix . 'chat_message_reactions';
        
        return $wpdb->get_results($wpdb->prepare(
            "SELECT reaction, COUNT(*) as count, GROUP_CONCAT(user_id) as user_ids
             FROM $reactions_table 
             WHERE message_id = %d 
             GROUP BY reaction",
            $message_id
        ));
    }
    
    /**
     * Check if user can send message in room
     */
    private function can_user_send_message($user_id, $room_id) {
        // For now, all logged-in users can send messages
        // This can be extended with room-specific permissions
        return is_user_logged_in() && $user_id > 0;
    }
    
    /**
     * Format message date
     */
    private function format_message_date($date) {
        $timestamp = strtotime($date);
        $now = current_time('timestamp');
        $diff = $now - $timestamp;
        
        if ($diff < 60) {
            return __('Just now', 'site-chat-system');
        } elseif ($diff < 3600) {
            $minutes = floor($diff / 60);
            return sprintf(_n('%d minute ago', '%d minutes ago', $minutes, 'site-chat-system'), $minutes);
        } elseif ($diff < 86400) {
            $hours = floor($diff / 3600);
            return sprintf(_n('%d hour ago', '%d hours ago', $hours, 'site-chat-system'), $hours);
        } else {
            return date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $timestamp);
        }
    }
    
    /**
     * Get default chat room
     */
    public function get_default_room() {
        global $wpdb;
        
        $rooms_table = $wpdb->prefix . 'chat_rooms';
        
        return $wpdb->get_row(
            "SELECT * FROM $rooms_table WHERE type = 'public' AND is_active = 1 ORDER BY id ASC LIMIT 1"
        );
    }
    
    /**
     * Cleanup old messages
     */
    public function cleanup_old_messages() {
        $database = new Site_Chat_Database();
        $database->cleanup_old_messages();
    }
    
    /**
     * Cleanup offline users
     */
    public function cleanup_offline_users() {
        $database = new Site_Chat_Database();
        $database->cleanup_offline_users();
    }

    /**
     * AJAX: Send chat message
     */
    public function ajax_send_message() {
        check_ajax_referer('site_chat_nonce', 'nonce');

        if (!is_user_logged_in()) {
            wp_send_json_error(__('You must be logged in to send messages.', 'site-chat-system'));
        }

        $room_id = intval($_POST['room_id'] ?? 1);
        $message = sanitize_textarea_field($_POST['message'] ?? '');
        $user_id = get_current_user_id();

        if (empty($message)) {
            wp_send_json_error(__('Message cannot be empty.', 'site-chat-system'));
        }

        $result = $this->send_message($room_id, $user_id, $message);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(array(
                'message_id' => $result,
                'message' => __('Message sent successfully.', 'site-chat-system')
            ));
        }
    }

    /**
     * AJAX: Get chat messages
     */
    public function ajax_get_messages() {
        check_ajax_referer('site_chat_nonce', 'nonce');

        if (!is_user_logged_in()) {
            wp_send_json_error(__('You must be logged in.', 'site-chat-system'));
        }

        $room_id = intval($_POST['room_id'] ?? 1);
        $since_id = intval($_POST['since_id'] ?? 0);
        $limit = intval($_POST['limit'] ?? 50);

        $messages = $this->get_messages($room_id, $limit, 0, $since_id);

        wp_send_json_success($messages);
    }

    /**
     * AJAX: Get online users
     */
    public function ajax_get_online_users() {
        check_ajax_referer('site_chat_nonce', 'nonce');

        if (!is_user_logged_in()) {
            wp_send_json_error(__('You must be logged in.', 'site-chat-system'));
        }

        $users = $this->get_online_users();

        // Add avatar URLs
        foreach ($users as &$user) {
            $user->avatar_url = get_avatar_url($user->user_id, array('size' => 32));
        }

        wp_send_json_success($users);
    }

    /**
     * AJAX: Update user status
     */
    public function ajax_update_user_status() {
        check_ajax_referer('site_chat_nonce', 'nonce');

        if (!is_user_logged_in()) {
            wp_send_json_error(__('You must be logged in.', 'site-chat-system'));
        }

        $status = sanitize_text_field($_POST['status'] ?? 'online');
        $user_id = get_current_user_id();

        $this->update_user_online_status($user_id, $status);

        wp_send_json_success(__('Status updated.', 'site-chat-system'));
    }

    /**
     * AJAX: Delete message
     */
    public function ajax_delete_message() {
        check_ajax_referer('site_chat_nonce', 'nonce');

        if (!is_user_logged_in()) {
            wp_send_json_error(__('You must be logged in.', 'site-chat-system'));
        }

        $message_id = intval($_POST['message_id'] ?? 0);
        $user_id = get_current_user_id();

        $result = $this->delete_message($message_id, $user_id);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(__('Message deleted.', 'site-chat-system'));
        }
    }

    /**
     * AJAX: Edit message
     */
    public function ajax_edit_message() {
        check_ajax_referer('site_chat_nonce', 'nonce');

        if (!is_user_logged_in()) {
            wp_send_json_error(__('You must be logged in.', 'site-chat-system'));
        }

        $message_id = intval($_POST['message_id'] ?? 0);
        $new_content = sanitize_textarea_field($_POST['content'] ?? '');
        $user_id = get_current_user_id();

        $result = $this->edit_message($message_id, $user_id, $new_content);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(__('Message edited.', 'site-chat-system'));
        }
    }

    /**
     * AJAX: React to message
     */
    public function ajax_react_to_message() {
        check_ajax_referer('site_chat_nonce', 'nonce');

        if (!is_user_logged_in()) {
            wp_send_json_error(__('You must be logged in.', 'site-chat-system'));
        }

        $message_id = intval($_POST['message_id'] ?? 0);
        $reaction = sanitize_text_field($_POST['reaction'] ?? '');
        $user_id = get_current_user_id();

        $result = $this->react_to_message($message_id, $user_id, $reaction);

        if ($result === false) {
            wp_send_json_error(__('Failed to add reaction.', 'site-chat-system'));
        } else {
            wp_send_json_success(array(
                'action' => $result,
                'reactions' => $this->get_message_reactions($message_id)
            ));
        }
    }

    /**
     * AJAX: Block user
     */
    public function ajax_block_user() {
        check_ajax_referer('site_chat_nonce', 'nonce');

        if (!is_user_logged_in()) {
            wp_send_json_error(__('You must be logged in.', 'site-chat-system'));
        }

        // This would implement user blocking functionality
        wp_send_json_success(__('User blocked.', 'site-chat-system'));
    }

    /**
     * AJAX: Upload file
     */
    public function ajax_upload_file() {
        check_ajax_referer('site_chat_nonce', 'nonce');

        if (!is_user_logged_in()) {
            wp_send_json_error(__('You must be logged in.', 'site-chat-system'));
        }

        // This would implement file upload functionality
        wp_send_json_success(__('File uploaded.', 'site-chat-system'));
    }
}
