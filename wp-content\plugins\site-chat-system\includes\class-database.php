<?php
/**
 * Database management class for Site Chat System
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Site_Chat_Database {
    
    /**
     * Database version
     */
    const DB_VERSION = '1.0.0';
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('plugins_loaded', array($this, 'check_database_version'));
    }
    
    /**
     * Check if database needs updating
     */
    public function check_database_version() {
        $installed_version = get_option('site_chat_db_version', '0.0.0');
        
        if (version_compare($installed_version, self::DB_VERSION, '<')) {
            $this->create_tables();
            update_option('site_chat_db_version', self::DB_VERSION);
        }
    }
    
    /**
     * Create database tables
     */
    public function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Chat rooms table
        $rooms_table = $wpdb->prefix . 'chat_rooms';
        $rooms_sql = "CREATE TABLE $rooms_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            name varchar(100) NOT NULL,
            description text,
            type enum('public', 'private', 'group') DEFAULT 'public',
            created_by bigint(20) unsigned NOT NULL,
            created_date datetime DEFAULT CURRENT_TIMESTAMP,
            is_active tinyint(1) DEFAULT 1,
            PRIMARY KEY (id),
            KEY created_by (created_by),
            KEY type (type),
            KEY is_active (is_active)
        ) $charset_collate;";
        
        // Chat messages table
        $messages_table = $wpdb->prefix . 'chat_messages';
        $messages_sql = "CREATE TABLE $messages_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            room_id bigint(20) unsigned NOT NULL,
            user_id bigint(20) unsigned NOT NULL,
            message text NOT NULL,
            message_type enum('text', 'image', 'file', 'system') DEFAULT 'text',
            file_url varchar(255),
            file_name varchar(255),
            file_size int(11),
            reply_to bigint(20) unsigned,
            is_edited tinyint(1) DEFAULT 0,
            edited_date datetime,
            created_date datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY room_id (room_id),
            KEY user_id (user_id),
            KEY created_date (created_date),
            KEY reply_to (reply_to)
        ) $charset_collate;";
        
        // Room participants table
        $participants_table = $wpdb->prefix . 'chat_room_participants';
        $participants_sql = "CREATE TABLE $participants_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            room_id bigint(20) unsigned NOT NULL,
            user_id bigint(20) unsigned NOT NULL,
            role enum('admin', 'moderator', 'member') DEFAULT 'member',
            joined_date datetime DEFAULT CURRENT_TIMESTAMP,
            last_seen datetime DEFAULT CURRENT_TIMESTAMP,
            is_active tinyint(1) DEFAULT 1,
            PRIMARY KEY (id),
            UNIQUE KEY unique_room_user (room_id, user_id),
            KEY room_id (room_id),
            KEY user_id (user_id),
            KEY last_seen (last_seen)
        ) $charset_collate;";
        
        // User online status table
        $online_users_table = $wpdb->prefix . 'chat_online_users';
        $online_users_sql = "CREATE TABLE $online_users_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            user_id bigint(20) unsigned NOT NULL,
            last_activity datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            status enum('online', 'away', 'busy', 'offline') DEFAULT 'online',
            PRIMARY KEY (id),
            UNIQUE KEY unique_user (user_id),
            KEY last_activity (last_activity),
            KEY status (status)
        ) $charset_collate;";
        
        // Message reactions table
        $reactions_table = $wpdb->prefix . 'chat_message_reactions';
        $reactions_sql = "CREATE TABLE $reactions_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            message_id bigint(20) unsigned NOT NULL,
            user_id bigint(20) unsigned NOT NULL,
            reaction varchar(10) NOT NULL,
            created_date datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY unique_message_user_reaction (message_id, user_id, reaction),
            KEY message_id (message_id),
            KEY user_id (user_id)
        ) $charset_collate;";
        
        // Blocked users table
        $blocked_users_table = $wpdb->prefix . 'chat_blocked_users';
        $blocked_users_sql = "CREATE TABLE $blocked_users_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            blocker_id bigint(20) unsigned NOT NULL,
            blocked_id bigint(20) unsigned NOT NULL,
            reason text,
            created_date datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY unique_blocker_blocked (blocker_id, blocked_id),
            KEY blocker_id (blocker_id),
            KEY blocked_id (blocked_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        
        dbDelta($rooms_sql);
        dbDelta($messages_sql);
        dbDelta($participants_sql);
        dbDelta($online_users_sql);
        dbDelta($reactions_sql);
        dbDelta($blocked_users_sql);
        
        // Create default public room
        $this->create_default_room();
    }
    
    /**
     * Create default public chat room
     */
    private function create_default_room() {
        global $wpdb;
        
        $rooms_table = $wpdb->prefix . 'chat_rooms';
        
        // Check if default room already exists
        $existing_room = $wpdb->get_var(
            "SELECT id FROM $rooms_table WHERE type = 'public' AND name = 'General Chat' LIMIT 1"
        );
        
        if (!$existing_room) {
            // Get admin user ID or use 1 as fallback
            $admin_user = get_users(array('role' => 'administrator', 'number' => 1));
            $admin_id = !empty($admin_user) ? $admin_user[0]->ID : 1;
            
            $wpdb->insert(
                $rooms_table,
                array(
                    'name' => 'General Chat',
                    'description' => 'Main chat room for all registered users',
                    'type' => 'public',
                    'created_by' => $admin_id,
                    'created_date' => current_time('mysql'),
                    'is_active' => 1
                )
            );
        }
    }
    
    /**
     * Drop all plugin tables
     */
    public function drop_tables() {
        global $wpdb;
        
        $tables = array(
            $wpdb->prefix . 'chat_blocked_users',
            $wpdb->prefix . 'chat_message_reactions',
            $wpdb->prefix . 'chat_online_users',
            $wpdb->prefix . 'chat_room_participants',
            $wpdb->prefix . 'chat_messages',
            $wpdb->prefix . 'chat_rooms'
        );
        
        foreach ($tables as $table) {
            $wpdb->query("DROP TABLE IF EXISTS $table");
        }
        
        delete_option('site_chat_db_version');
    }
    
    /**
     * Get table name with prefix
     */
    public function get_table_name($table) {
        global $wpdb;
        return $wpdb->prefix . 'chat_' . $table;
    }
    
    /**
     * Clean up old messages
     */
    public function cleanup_old_messages() {
        global $wpdb;
        
        $options = get_option('site_chat_options', array());
        
        if (!($options['auto_delete_messages'] ?? false)) {
            return;
        }
        
        $days = intval($options['delete_messages_after_days'] ?? 30);
        $cutoff_date = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        $messages_table = $wpdb->prefix . 'chat_messages';
        
        $wpdb->query($wpdb->prepare(
            "DELETE FROM $messages_table WHERE created_date < %s",
            $cutoff_date
        ));
    }
    
    /**
     * Update user online status
     */
    public function update_user_online_status($user_id, $status = 'online') {
        global $wpdb;
        
        $online_users_table = $wpdb->prefix . 'chat_online_users';
        
        $wpdb->replace(
            $online_users_table,
            array(
                'user_id' => $user_id,
                'last_activity' => current_time('mysql'),
                'status' => $status
            )
        );
    }
    
    /**
     * Get online users
     */
    public function get_online_users($minutes = 5) {
        global $wpdb;
        
        $online_users_table = $wpdb->prefix . 'chat_online_users';
        $cutoff_time = date('Y-m-d H:i:s', strtotime("-{$minutes} minutes"));
        
        return $wpdb->get_results($wpdb->prepare(
            "SELECT ou.*, u.display_name, u.user_email
             FROM $online_users_table ou
             INNER JOIN {$wpdb->users} u ON ou.user_id = u.ID
             WHERE ou.last_activity > %s AND ou.status != 'offline'
             ORDER BY ou.last_activity DESC",
            $cutoff_time
        ));
    }
    
    /**
     * Clean up offline users
     */
    public function cleanup_offline_users($minutes = 10) {
        global $wpdb;
        
        $online_users_table = $wpdb->prefix . 'chat_online_users';
        $cutoff_time = date('Y-m-d H:i:s', strtotime("-{$minutes} minutes"));
        
        $wpdb->query($wpdb->prepare(
            "DELETE FROM $online_users_table WHERE last_activity < %s",
            $cutoff_time
        ));
    }
}
