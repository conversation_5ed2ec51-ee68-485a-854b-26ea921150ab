=== Classic Widgets ===
Contributors: wordpressdotorg, hellofromtonya, azaozz
Tags: gutenberg, disable, disable gut<PERSON>, editor, classic widgets
Requires at least: 4.9
Tested up to: 5.9
Stable tag: 0.3
Requires PHP: 5.6 or later
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Enables the previous "classic" widgets settings screens in Appearance - Widgets and the Customizer. Disables the block editor from managing widgets.

== Description ==

Classic Widgets is an official plugin maintained by the WordPress team that restores the previous ("classic") WordPress widgets settings screens. It will be supported and maintained until at least 2022, or as long as is necessary.

Once activated and when using a classic (non-block) theme, this plugin restores the previous widgets settings screens and disables the block editor from managing widgets. There is no other configuration, the classic widgets settings screens are enabled or disabled by either enabling or disabling this plugin.

== Changelog ==

= 0.3 =
Update for 5.9.

= 0.2 =
Update filter name.

= 0.1 =
Initial release.

== Frequently Asked Questions ==

= Are there any settings? =

No, there are no settings. Once activated, this plugin restores the previous ("classic") WordPress widgets screen and disables the block editor from managing widgets.

= Does this work with full site editing and block themes? =

No, as a block themes relies on blocks. [See Block themes article](https://wordpress.org/support/article/block-themes/) for more information.
