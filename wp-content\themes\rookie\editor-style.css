body#tinymce.wp-editor {
	font-family: "Lato", sans-serif;
	font-size: 16px;
	line-height: 1.5;
}

body#tinymce.wp-editor h1,
body#tinymce.wp-editor h2,
body#tinymce.wp-editor h3,
body#tinymce.wp-editor h4,
body#tinymce.wp-editor h5,
body#tinymce.wp-editor h6 {
	clear: both;
	font-family: "<PERSON>", sans-serif;
	text-transform: uppercase;
	margin-bottom: 0.5em;
}

body#tinymce.wp-editor h1 {
	font-size: 36px;
}

body#tinymce.wp-editor h2 {
	font-size: 28px;
}

body#tinymce.wp-editor h3 {
	font-size: 24px;
}

body#tinymce.wp-editor h4 {
	font-size: 20px;
}

body#tinymce.wp-editor h5 {
	font-size: 18px;
}

body#tinymce.wp-editor h6 {
	font-size: 16px;
}

body#tinymce.wp-editor p {
	margin-bottom: 1.5em;
}

body#tinymce.wp-editor b,
body#tinymce.wp-editor strong {
	font-weight: bold;
}

body#tinymce.wp-editor dfn,
body#tinymce.wp-editor cite,
body#tinymce.wp-editor em,
body#tinymce.wp-editor i,
body#tinymce.wp-editor blockquote,
body#tinymce.wp-editor q {
	font-style: italic;
}

body#tinymce.wp-editor blockquote,
body#tinymce.wp-editor q {
	margin: 0 1.5em 1.5em 3em;
	position: relative;
	font-weight: bold;
	font-size: 18px;
}

body#tinymce.wp-editor blockquote p {
	display: inline;
}

body#tinymce.wp-editor cite {
	display: block;
	font-weight: normal;
	font-size: 14px;
	position: relative;
	text-indent: 2.5em;
	margin-top: 0.5em;
}

body#tinymce.wp-editor address {
	margin: 0 0 1.5em;
}

body#tinymce.wp-editor pre {
	background: #f4f4f4;
	border: 1px solid #e0e0e0;
	font-family: "Courier 10 Pitch", Courier, monospace;
	font-size: 14px;
	line-height: 1.6;
	margin-bottom: 1.6em;
	max-width: 100%;
	overflow: auto;
	padding: 15px;
}

body#tinymce.wp-editor code,
body#tinymce.wp-editor kbd,
body#tinymce.wp-editor tt,
body#tinymce.wp-editor var {
	font-family: Monaco, Consolas, "Andale Mono", "DejaVu Sans Mono", monospace;
	font-size: 14px;
	background: #f4f4f4;
	border: 1px solid #e0e0e0;
	padding: 1px 3px;
}

body#tinymce.wp-editor abbr,
body#tinymce.wp-editor acronym {
	border-bottom: 1px dotted #666;
	cursor: help;
}

body#tinymce.wp-editor mark,
body#tinymce.wp-editor ins {
	background: #fff9c0;
	text-decoration: none;
}

body#tinymce.wp-editor sup,
body#tinymce.wp-editor sub {
	font-size: 75%;
	height: 0;
	line-height: 0;
	position: relative;
	vertical-align: baseline;
}

body#tinymce.wp-editor sup {
	bottom: 1ex;
}

body#tinymce.wp-editor sub {
	top: .5ex;
}

body#tinymce.wp-editor small {
	font-size: 75%;
}

body#tinymce.wp-editor big {
	font-size: 125%;
}

body#tinymce.wp-editor table {
	margin: 0 0 1.5em;
	width: 100%;
	background: #f4f4f4;
	border-top: 1px solid #e0e0e0;
}

body#tinymce.wp-editor caption {
	font-size: 16px;
	font-family: "Oswald", sans-serif;
	text-transform: uppercase;
	padding: 0.625em 15px;
	margin: 0 0 -1px;
}

body#tinymce.wp-editor th,
body#tinymce.wp-editor td {
	font-size: 14px;
	padding: 0.625em;
	border-bottom: 1px solid #e0e0e0;
	text-align: center;
}

body#tinymce.wp-editor thead th {
	font-weight: bold;
}

body#tinymce.wp-editor tbody td {
	border-right: 1px solid #e0e0e0;
}

body#tinymce.wp-editor th:first-child,
body#tinymce.wp-editor td:first-child {
	border-left: 1px solid #e0e0e0;
}

body#tinymce.wp-editor th:last-child,
body#tinymce.wp-editor td:last-child {
	border-right: 1px solid #e0e0e0;
}

