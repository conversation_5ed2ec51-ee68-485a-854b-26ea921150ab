<?php
/**
 * Utility functions and helpers
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class COD_Clan_League_Utilities {
    
    /**
     * Format match result for display
     */
    public static function format_match_result($match) {
        if (!$match) {
            return '';
        }
        
        $home_score = intval($match->home_score ?? 0);
        $away_score = intval($match->away_score ?? 0);
        
        return sprintf('%d - %d', $home_score, $away_score);
    }
    
    /**
     * Get match status display text
     */
    public static function get_match_status_text($status) {
        $statuses = array(
            'scheduled' => __('Scheduled', 'cod-clan-league'),
            'pending_approval' => __('Pending Approval', 'cod-clan-league'),
            'completed' => __('Completed', 'cod-clan-league'),
            'cancelled' => __('Cancelled', 'cod-clan-league'),
        );
        
        return $statuses[$status] ?? ucfirst(str_replace('_', ' ', $status));
    }
    
    /**
     * Get season status display text
     */
    public static function get_season_status_text($status) {
        $statuses = array(
            'upcoming' => __('Upcoming', 'cod-clan-league'),
            'active' => __('Active', 'cod-clan-league'),
            'completed' => __('Completed', 'cod-clan-league'),
        );
        
        return $statuses[$status] ?? ucfirst($status);
    }
    
    /**
     * Get clan status display text
     */
    public static function get_clan_status_text($status) {
        $statuses = array(
            'pending' => __('Pending', 'cod-clan-league'),
            'active' => __('Active', 'cod-clan-league'),
            'inactive' => __('Inactive', 'cod-clan-league'),
        );
        
        return $statuses[$status] ?? ucfirst($status);
    }
    
    /**
     * Calculate win percentage
     */
    public static function calculate_win_percentage($wins, $total_matches) {
        if ($total_matches == 0) {
            return 0;
        }
        
        return round(($wins / $total_matches) * 100, 1);
    }
    
    /**
     * Get team form (last 5 matches)
     */
    public static function get_team_form($team_id, $limit = 5) {
        global $wpdb;
        
        $matches_table = $wpdb->prefix . 'cod_matches';
        
        $matches = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $matches_table 
             WHERE (home_team_id = %d OR away_team_id = %d) AND status = 'completed'
             ORDER BY completed_date DESC
             LIMIT %d",
            $team_id, $team_id, $limit
        ));
        
        $form = array();
        foreach ($matches as $match) {
            if ($match->winner_team_id == $team_id) {
                $form[] = 'W';
            } elseif ($match->winner_team_id === null) {
                $form[] = 'D';
            } else {
                $form[] = 'L';
            }
        }
        
        return $form;
    }
    
    /**
     * Generate unique invitation code
     */
    public static function generate_invitation_code($length = 32) {
        return wp_generate_password($length, false);
    }
    
    /**
     * Format date for display
     */
    public static function format_date($date, $format = null) {
        if (!$date) {
            return '';
        }
        
        if (!$format) {
            $format = get_option('date_format') . ' ' . get_option('time_format');
        }
        
        return date_i18n($format, strtotime($date));
    }
    
    /**
     * Get time ago string
     */
    public static function time_ago($date) {
        if (!$date) {
            return '';
        }
        
        return sprintf(__('%s ago', 'cod-clan-league'), human_time_diff(strtotime($date), current_time('timestamp')));
    }
    
    /**
     * Truncate text with ellipsis
     */
    public static function truncate_text($text, $length = 100, $suffix = '...') {
        if (strlen($text) <= $length) {
            return $text;
        }
        
        return substr($text, 0, $length) . $suffix;
    }
    
    /**
     * Get avatar URL for user
     */
    public static function get_user_avatar_url($user_id, $size = 96) {
        return get_avatar_url($user_id, array('size' => $size));
    }
    
    /**
     * Generate CSS classes for status
     */
    public static function get_status_classes($status, $prefix = 'cod-status') {
        $classes = array($prefix, $prefix . '-' . $status);
        
        // Add semantic classes
        switch ($status) {
            case 'active':
            case 'completed':
            case 'approved':
                $classes[] = $prefix . '-success';
                break;
                
            case 'pending':
            case 'pending_approval':
            case 'upcoming':
                $classes[] = $prefix . '-warning';
                break;
                
            case 'inactive':
            case 'cancelled':
            case 'declined':
                $classes[] = $prefix . '-danger';
                break;
        }
        
        return implode(' ', $classes);
    }
    
    /**
     * Validate and sanitize color hex code
     */
    public static function sanitize_hex_color($color) {
        if (empty($color)) {
            return '';
        }
        
        // Remove # if present
        $color = ltrim($color, '#');
        
        // Validate hex color
        if (!preg_match('/^[a-fA-F0-9]{6}$/', $color)) {
            return '';
        }
        
        return '#' . $color;
    }
    
    /**
     * Get plugin option with default
     */
    public static function get_option($key, $default = null) {
        $options = get_option('cod_clan_league_options', array());
        return $options[$key] ?? $default;
    }
    
    /**
     * Update plugin option
     */
    public static function update_option($key, $value) {
        $options = get_option('cod_clan_league_options', array());
        $options[$key] = $value;
        return update_option('cod_clan_league_options', $options);
    }
    
    /**
     * Get current season
     */
    public static function get_current_season() {
        global $wpdb;
        
        $seasons_table = $wpdb->prefix . 'cod_seasons';
        
        return $wpdb->get_row(
            "SELECT * FROM $seasons_table 
             WHERE status = 'active' 
             ORDER BY start_date DESC 
             LIMIT 1"
        );
    }
    
    /**
     * Get upcoming matches for a team
     */
    public static function get_upcoming_matches($team_id, $limit = 5) {
        global $wpdb;
        
        $matches_table = $wpdb->prefix . 'cod_matches';
        
        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $matches_table 
             WHERE (home_team_id = %d OR away_team_id = %d) 
             AND status = 'scheduled' 
             AND scheduled_date > NOW()
             ORDER BY scheduled_date ASC
             LIMIT %d",
            $team_id, $team_id, $limit
        ));
    }
    
    /**
     * Check if user is clan member
     */
    public static function is_user_clan_member($user_id, $clan_id) {
        global $wpdb;
        
        $members_table = $wpdb->prefix . 'cod_clan_members';
        
        $result = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $members_table 
             WHERE clan_id = %d AND user_id = %d AND status = 'active'",
            $clan_id, $user_id
        ));
        
        return $result > 0;
    }
    
    /**
     * Get clan by user ID
     */
    public static function get_user_clan($user_id) {
        global $wpdb;
        
        $members_table = $wpdb->prefix . 'cod_clan_members';
        $clans_table = $wpdb->prefix . 'cod_clans';
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT c.* FROM $clans_table c
             INNER JOIN $members_table cm ON c.id = cm.clan_id
             WHERE cm.user_id = %d AND cm.status = 'active'
             LIMIT 1",
            $user_id
        ));
    }
    
    /**
     * Generate league table position suffix
     */
    public static function get_position_suffix($position) {
        $suffixes = array(
            1 => 'st',
            2 => 'nd',
            3 => 'rd',
        );
        
        if (isset($suffixes[$position])) {
            return $position . $suffixes[$position];
        }
        
        return $position . 'th';
    }
    
    /**
     * Calculate points per game
     */
    public static function calculate_points_per_game($points, $games_played) {
        if ($games_played == 0) {
            return 0;
        }
        
        return round($points / $games_played, 2);
    }
    
    /**
     * Get match week number
     */
    public static function get_match_week($match_date, $season_start) {
        $start_timestamp = strtotime($season_start);
        $match_timestamp = strtotime($match_date);
        
        $diff_days = ($match_timestamp - $start_timestamp) / (24 * 60 * 60);
        
        return ceil($diff_days / 7);
    }
    
    /**
     * Format file size
     */
    public static function format_file_size($bytes) {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }
    
    /**
     * Generate random team colors
     */
    public static function generate_team_colors() {
        $colors = array(
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
            '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2'
        );
        
        return $colors[array_rand($colors)];
    }
    
    /**
     * Validate email address
     */
    public static function validate_email($email) {
        return is_email($email);
    }
    
    /**
     * Send notification email
     */
    public static function send_notification_email($to, $subject, $message, $headers = array()) {
        $default_headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . get_option('blogname') . ' <' . get_option('admin_email') . '>'
        );
        
        $headers = array_merge($default_headers, $headers);
        
        return wp_mail($to, $subject, $message, $headers);
    }
    
    /**
     * Get plugin version
     */
    public static function get_plugin_version() {
        return COD_CLAN_LEAGUE_VERSION;
    }
    
    /**
     * Check if plugin is in debug mode
     */
    public static function is_debug_mode() {
        return defined('WP_DEBUG') && WP_DEBUG;
    }
    
    /**
     * Log debug message
     */
    public static function debug_log($message, $data = null) {
        if (!self::is_debug_mode()) {
            return;
        }
        
        $log_message = '[COD Clan League] ' . $message;
        
        if ($data !== null) {
            $log_message .= ' - Data: ' . print_r($data, true);
        }
        
        error_log($log_message);
    }
}
