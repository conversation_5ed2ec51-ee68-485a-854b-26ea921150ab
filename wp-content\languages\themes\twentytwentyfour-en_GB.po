# Translation of Themes - Twenty Twenty-Four in English (UK)
# This file is distributed under the same license as the Themes - Twenty Twenty-Four package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-07-16 14:50:14+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: en_GB\n"
"Project-Id-Version: Themes - Twenty Twenty-Four\n"

#. Theme Name of the theme
#: style.css
#, gp-priority: high
msgid "Twenty Twenty-Four"
msgstr "Twenty Twenty-Four"

#. Description of the theme
#: style.css
#, gp-priority: high
msgid "Twenty Twenty-Four is designed to be flexible, versatile and applicable to any website. Its collection of templates and patterns tailor to different needs, such as presenting a business, blogging and writing or showcasing work. A multitude of possibilities open up with just a few adjustments to color and typography. Twenty Twenty-Four comes with style variations and full page designs to help speed up the site building process, is fully compatible with the site editor, and takes advantage of new design tools introduced in WordPress 6.4."
msgstr "Twenty Twenty-Four is designed to be flexible, versatile and applicable to any website. Its collection of templates and patterns tailor to different needs, such as presenting a business, blogging and writing or showcasing work. A multitude of possibilities open up with just a few adjustments to colour and typography. Twenty Twenty-Four comes with style variations and full page designs to help speed up the site building process, is fully compatible with the site editor, and takes advantage of new design tools introduced in WordPress 6.4."

#: patterns/footer.php:49
msgid "Team"
msgstr "Team"

#: patterns/footer.php:50
msgid "History"
msgstr "History"

#: patterns/footer.php:51
msgid "Careers"
msgstr "Careers"

#: patterns/footer.php:72
msgid "Privacy Policy"
msgstr "Privacy Policy"

#: patterns/footer.php:73
msgid "Terms and Conditions"
msgstr "Terms and Conditions"

#: patterns/footer.php:74
msgid "Contact Us"
msgstr "Contact Us"

#: patterns/footer.php:96
msgid "Twitter/X"
msgstr "X"

#: functions.php:199
msgctxt "Block pattern category"
msgid "Pages"
msgstr "Pages"

#: functions.php:200
msgid "A collection of full page layouts."
msgstr "A collection of full-page layouts."

#: patterns/hidden-portfolio-hero.php:16
msgid "I’m <em>Leia Acosta</em>, a passionate photographer who finds inspiration in capturing the fleeting beauty of life."
msgstr "I’m <em>Leia Acosta</em>, a passionate photographer who finds inspiration in capturing the fleeting beauty of life."

#: patterns/posts-list.php
msgctxt "Pattern title"
msgid "List of posts without images, 1 column"
msgstr "List of posts without images, 1 column"

#. Author URI of the theme
#. Translators: WordPress link.
#: style.css patterns/footer-centered-logo-nav.php:22
#: patterns/footer-colophon-3-col.php:91 patterns/footer.php:117
msgid "https://wordpress.org"
msgstr "https://en-gb.wordpress.org"

#: theme.json
msgctxt "Template part name"
msgid "Header"
msgstr "Header"

#: theme.json
msgctxt "Template part name"
msgid "Footer"
msgstr "Footer"

#: styles/fossil.json styles/ice.json theme.json
msgctxt "Font family name"
msgid "Inter"
msgstr "Inter"

#: theme.json
msgctxt "Template part name"
msgid "Post Meta"
msgstr "Post Meta"

#: styles/fossil.json styles/ice.json styles/maelstrom.json theme.json
msgctxt "Font size name"
msgid "Extra Large"
msgstr "Extra Large"

#: styles/fossil.json styles/ice.json styles/maelstrom.json theme.json
msgctxt "Font size name"
msgid "Large"
msgstr "Large"

#: styles/fossil.json styles/ice.json styles/maelstrom.json theme.json
msgctxt "Font size name"
msgid "Medium"
msgstr "Medium"

#: styles/ember.json styles/fossil.json styles/ice.json styles/maelstrom.json
#: styles/mint.json styles/onyx.json styles/rust.json theme.json
msgctxt "Color name"
msgid "Contrast"
msgstr "Contrast"

#: styles/ember.json styles/fossil.json styles/ice.json styles/maelstrom.json
#: styles/mint.json styles/onyx.json styles/rust.json theme.json
msgctxt "Color name"
msgid "Base"
msgstr "Base"

#: theme.json
msgctxt "Space size name"
msgid "6"
msgstr "6"

#: theme.json
msgctxt "Space size name"
msgid "5"
msgstr "5"

#: theme.json
msgctxt "Space size name"
msgid "4"
msgstr "4"

#: theme.json
msgctxt "Space size name"
msgid "3"
msgstr "3"

#: theme.json
msgctxt "Space size name"
msgid "2"
msgstr "2"

#: theme.json
msgctxt "Space size name"
msgid "1"
msgstr "1"

#: functions.php:28
msgid "Arrow icon"
msgstr "Arrow icon"

#: functions.php:51
msgid "Pill"
msgstr "Pill"

#: functions.php:74
msgid "Checkmark"
msgstr "Checkmark"

#: functions.php:93
msgid "With arrow"
msgstr "With arrow"

#: functions.php:111
msgid "With asterisk"
msgstr "With asterisk"

#: patterns/banner-hero.php
msgctxt "Pattern title"
msgid "Hero"
msgstr "Hero"

#: patterns/banner-hero.php:18
msgctxt "Heading of the hero section"
msgid "A commitment to innovation and sustainability"
msgstr "A commitment to innovation and sustainability"

#: patterns/banner-hero.php:26
msgctxt "Content of the hero section"
msgid "Études is a pioneering firm that seamlessly merges creativity and functionality to redefine architectural excellence."
msgstr "Études is a pioneering firm that seamlessly merges creativity and functionality to redefine architectural excellence."

#: patterns/banner-hero.php:37
msgctxt "Button text of the hero section"
msgid "About us"
msgstr "About us"

#: patterns/banner-hero.php:52
msgid "Building exterior in Toronto, Canada"
msgstr "Building exterior in Toronto, Canada"

#: patterns/banner-project-description.php
msgctxt "Pattern title"
msgid "Project description"
msgstr "Project description"

#: patterns/banner-project-description.php:17
msgctxt "Sample title for a project or post"
msgid "Art Gallery — Overview"
msgstr "Art Gallery – Overview"

#: patterns/banner-project-description.php:26
msgctxt "Sample descriptive text for a project or post."
msgid "This transformative project seeks to enhance the gallery's infrastructure, accessibility, and exhibition spaces while preserving its rich cultural heritage."
msgstr "This transformative project seeks to enhance the gallery's infrastructure, accessibility, and exhibition spaces while preserving its rich cultural heritage."

#: patterns/banner-project-description.php:41
msgid "Hyatt Regency San Francisco, San Francisco, United States"
msgstr "Hyatt Regency San Francisco, San Francisco, United States"

#: patterns/cta-content-image-on-right.php
msgctxt "Pattern title"
msgid "Call to action with image on right"
msgstr "Call to action with image on right"

#: patterns/cta-content-image-on-right.php:18
msgctxt "Sample heading"
msgid "Enhance your architectural journey with the Études Architect app."
msgstr "Enhance your architectural journey with the Études Architect app."

#: patterns/cta-content-image-on-right.php:24
#: patterns/text-alternating-images.php:44
msgctxt "Sample list item"
msgid "Collaborate with fellow architects."
msgstr "Collaborate with fellow architects."

#: patterns/cta-content-image-on-right.php:28
#: patterns/text-alternating-images.php:48
msgctxt "Sample list item"
msgid "Showcase your projects."
msgstr "Showcase your projects."

#: patterns/cta-content-image-on-right.php:32
#: patterns/text-alternating-images.php:52
msgctxt "Sample list item"
msgid "Experience the world of architecture."
msgstr "Experience the world of architecture."

#: patterns/cta-content-image-on-right.php:41
msgctxt "Button text of this section"
msgid "Download app"
msgstr "Download app"

#: patterns/cta-content-image-on-right.php:47
msgctxt "Button text of this section"
msgid "How it works"
msgstr "How it works"

#: patterns/cta-content-image-on-right.php:59
#: patterns/cta-services-image-left.php:19
msgid "White abstract geometric artwork from Dresden, Germany"
msgstr "White abstract geometric artwork from Dresden, Germany"

#: patterns/cta-pricing.php
msgctxt "Pattern title"
msgid "Pricing"
msgstr "Pricing"

#: patterns/cta-pricing.php:11
msgctxt "Name for the pricing pattern"
msgid "Pricing Table"
msgstr "Pricing Table"

#: patterns/cta-pricing.php:18
msgctxt "Sample heading for pricing pattern"
msgid "Our Services"
msgstr "Our Services"

#: patterns/cta-pricing.php:22
msgctxt "Sample description for a pricing table"
msgid "We offer flexible options, which you can adapt to the different needs of each project."
msgstr "We offer flexible options, which you can adapt to the different needs of each project."

#: patterns/cta-pricing.php:37
msgctxt "Sample heading for the first pricing level"
msgid "Free"
msgstr "Free"

#: patterns/cta-pricing.php:42
msgctxt "Sample price for the first pricing level"
msgid "$0"
msgstr "£0"

#: patterns/cta-pricing.php:53
msgctxt "Feature for pricing level"
msgid "Access to 5 exclusive <em>Études Articles</em> per month."
msgstr "Access to five exclusive <em>Études Articles</em> per month."

#: patterns/cta-pricing.php:62 patterns/cta-pricing.php:123
#: patterns/cta-pricing.php:181
msgctxt "Feature for pricing level"
msgid "Weekly print edition."
msgstr "Weekly print edition."

#: patterns/cta-pricing.php:72 patterns/cta-pricing.php:131
msgctxt "Feature for pricing level"
msgid "Exclusive access to the <em>Études</em> app for iOS and Android."
msgstr "Exclusive access to the <em>Études</em> app for iOS and Android."

#: patterns/cta-pricing.php:87
msgctxt "Button text for the first pricing level"
msgid "Subscribe"
msgstr "Subscribe"

#: patterns/cta-pricing.php:99
msgctxt "Sample heading for the second pricing level"
msgid "Connoisseur"
msgstr "Connoisseur"

#: patterns/cta-pricing.php:104
msgctxt "Sample price for the second pricing level"
msgid "$12"
msgstr "£12"

#: patterns/cta-pricing.php:115
msgctxt "Feature for pricing level"
msgid "Access to 20 exclusive <em>Études Articles</em> per month."
msgstr "Access to 20 exclusive <em>Études Articles</em> per month."

#: patterns/cta-pricing.php:145
msgctxt "Button text for the second pricing level"
msgid "Subscribe"
msgstr "Subscribe"

#: patterns/cta-pricing.php:157
msgctxt "Sample heading for the third pricing level"
msgid "Expert"
msgstr "Expert"

#: patterns/cta-pricing.php:162
msgctxt "Sample price for the third pricing level"
msgid "$28"
msgstr "£28"

#: patterns/cta-pricing.php:173
msgctxt "Feature for pricing level"
msgid "Exclusive, unlimited access to <em>Études Articles</em>."
msgstr "Exclusive, unlimited access to <em>Études Articles</em>."

#: patterns/cta-pricing.php:189
msgctxt "Feature for pricing level"
msgid "Exclusive access to the <em>Études</em> app for iOS and Android"
msgstr "Exclusive access to the <em>Études</em> app for iOS and Android"

#: patterns/cta-pricing.php:203
msgctxt "Button text for the third pricing level"
msgid "Subscribe"
msgstr "Subscribe"

#: patterns/cta-rsvp.php
msgctxt "Pattern title"
msgid "RSVP"
msgstr "RSVP"

#: patterns/cta-rsvp.php:11
msgctxt "Name of RSVP pattern"
msgid "RSVP"
msgstr "RSVP"

#: patterns/cta-rsvp.php:21 patterns/page-rsvp-landing.php:23
msgctxt "Initials for ´please respond´"
msgid "RSVP"
msgstr "RSVP"

#: patterns/cta-rsvp.php:27 patterns/page-rsvp-landing.php:28
msgctxt "RSVP call to action description"
msgid "Experience the fusion of imagination and expertise with Études Arch Summit, February 2025."
msgstr "Experience the fusion of imagination and expertise with Études Arch Summit, February 2025."

#: patterns/cta-rsvp.php:34 patterns/page-rsvp-landing.php:34
msgctxt "Call to action button text for the reservation button"
msgid "Reserve your spot"
msgstr "Reserve your spot"

#: patterns/cta-rsvp.php:50 patterns/text-title-left-image-right.php:51
msgid "A ramp along a curved wall in the Kiasma Museu, Helsinki, Finland"
msgstr "A ramp along a curved wall in the Kiasma Museu, Helsinki, Finland"

#: patterns/cta-services-image-left.php
msgctxt "Pattern title"
msgid "Services call to action with image on left"
msgstr "Services call to action with image on left"

#: patterns/cta-services-image-left.php:28
msgctxt "Sample heading of the services pattern"
msgid "Guiding your business through the project"
msgstr "Guiding your business through the project"

#: patterns/cta-services-image-left.php:32
msgctxt "Sample description of the services pattern"
msgid "Experience the fusion of imagination and expertise with Études—the catalyst for architectural transformations that enrich the world around us."
msgstr "Experience the fusion of imagination and expertise with Études – the catalyst for architectural transformations that enrich the world around us."

#: patterns/cta-services-image-left.php:39
msgctxt "Sample button text to view the services"
msgid "Our services"
msgstr "Our services"

#: patterns/cta-subscribe-centered.php
msgctxt "Pattern title"
msgid "Centered call to action"
msgstr "Centred call to action"

#: patterns/cta-subscribe-centered.php:20
msgctxt "Sample text for Subscriber Heading with numbers"
msgid "Join 900+ subscribers"
msgstr "Join 900+ subscribers"

#: patterns/cta-subscribe-centered.php:24
msgctxt "Sample text for Subscriber Description"
msgid "Stay in the loop with everything you need to know."
msgstr "Stay in the loop with everything you need to know."

#: patterns/cta-subscribe-centered.php:31
msgctxt "Sample text for Sign Up Button"
msgid "Sign up"
msgstr "Sign up"

#: patterns/footer-centered-logo-nav.php
msgctxt "Pattern title"
msgid "Footer with centered logo and navigation"
msgstr "Footer with centred logo and navigation"

#. Translators: Designed with WordPress
#: patterns/footer-centered-logo-nav.php:25
#: patterns/footer-colophon-3-col.php:94 patterns/footer.php:120
msgid "Designed with %1$s"
msgstr "Designed with %1$s"

#: patterns/footer-colophon-3-col.php
msgctxt "Pattern title"
msgid "Footer with colophon, 3 columns"
msgstr "Footer with colophon, 3 columns"

#: patterns/footer-colophon-3-col.php:30
msgid "Keep up, get in touch."
msgstr "Keep up, get in touch."

#: patterns/footer-colophon-3-col.php:39
msgid "Contact"
msgstr "Contact"

#: patterns/footer-colophon-3-col.php:42
msgctxt "Example email in site footer"
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: patterns/footer-colophon-3-col.php:57
msgid "Follow"
msgstr "Follow"

#: patterns/footer-colophon-3-col.php:60 patterns/footer.php:95
msgid "Instagram"
msgstr "Instagram"

#: patterns/footer-colophon-3-col.php:60 patterns/footer.php:94
msgid "Facebook"
msgstr "Facebook"

#: patterns/footer-colophon-3-col.php:82
msgid "&copy;"
msgstr "&copy;"

#: patterns/footer.php
msgctxt "Pattern title"
msgid "Footer with colophon, 4 columns"
msgstr "Footer with colophon, 4 columns"

#: patterns/footer.php:41 patterns/footer.php:47
msgid "About"
msgstr "About"

#: patterns/footer.php:64 patterns/footer.php:70
msgid "Privacy"
msgstr "Privacy"

#: patterns/footer.php:86
msgid "Social"
msgstr "Social"

#: patterns/footer.php:92
msgid "Social Media"
msgstr "Social Media"

#: patterns/gallery-full-screen-image.php
msgctxt "Pattern title"
msgid "Full screen image"
msgstr "Full screen image"

#: patterns/gallery-offset-images-grid-2-col.php
msgctxt "Pattern title"
msgid "Offset gallery, 2 columns"
msgstr "Offset gallery, 2 columns"

#: patterns/gallery-offset-images-grid-3-col.php
msgctxt "Pattern title"
msgid "Offset gallery, 3 columns"
msgstr "Offset gallery, 3 columns"

#: patterns/gallery-offset-images-grid-4-col.php
msgctxt "Pattern title"
msgid "Offset gallery, 4 columns"
msgstr "Offset gallery, 4 columns"

#: patterns/gallery-project-layout.php
msgctxt "Pattern title"
msgid "Project layout"
msgstr "Project layout"

#: patterns/gallery-project-layout.php:21
msgid "An empty staircase under an angular roof in Darling Harbour, Sydney, Australia"
msgstr "An empty staircase under an angular roof in Darling Harbour, Sydney, Australia"

#: patterns/gallery-project-layout.php:26
msgctxt "Sample text for the feature area"
msgid "1. Through Études, we aspire to redefine architectural boundaries and usher in a new era of design excellence that leaves an indelible mark on the built environment."
msgstr "1. Through Études, we aspire to redefine architectural boundaries and usher in a new era of design excellence that leaves an indelible mark on the built environment."

#: patterns/gallery-project-layout.php:38
msgctxt "Sample text for the feature area"
msgid "Our comprehensive suite of professional services caters to a diverse clientele, ranging from homeowners to commercial developers. With a commitment to innovation and sustainability, Études is the bridge that transforms architectural dreams into remarkable built realities."
msgstr "Our comprehensive suite of professional services caters to a diverse clientele, ranging from homeowners to commercial developers. With a commitment to innovation and sustainability, Études is the bridge that transforms architectural dreams into remarkable built realities."

#: patterns/gallery-project-layout.php:49
msgctxt "Sample text for the feature area"
msgid "2. Case studies that celebrate the artistry can fuel curiosity and ignite inspiration."
msgstr "2. Case studies that celebrate the artistry can fuel curiosity and ignite inspiration."

#: patterns/gallery-project-layout.php:54
msgid "Art Gallery of Ontario, Toronto, Canada"
msgstr "Art Gallery of Ontario, Toronto, Canada"

#: patterns/hidden-404.php
msgctxt "Pattern title"
msgid "404"
msgstr "404"

#: patterns/hidden-404.php:10
msgctxt "Heading for a webpage that is not found"
msgid "Page Not Found"
msgstr "Page Not Found"

#: patterns/hidden-404.php:13
msgctxt "Message to convey that a webpage could not be found"
msgid "The page you are looking for does not exist, or it has been moved. Please try searching using the form below."
msgstr "The page you are looking for does not exist, or it has been moved. Please try searching using the form below."

#: patterns/hidden-comments.php
msgctxt "Pattern title"
msgid "Comments"
msgstr "Comments"

#: patterns/hidden-comments.php:12
msgid "Comments"
msgstr "Comments"

#: patterns/hidden-no-results.php
msgctxt "Pattern title"
msgid "No results"
msgstr "No results"

#: patterns/hidden-no-results.php:9
msgctxt "Message explaining that there are no results returned from a search"
msgid "No posts were found."
msgstr "No posts were found."

#: patterns/hidden-portfolio-hero.php
msgctxt "Pattern title"
msgid "Portfolio hero"
msgstr "Portfolio hero"

#: patterns/hidden-post-meta.php
msgctxt "Pattern title"
msgid "Post meta"
msgstr "Post meta"

#: patterns/hidden-post-meta.php:20
msgctxt "Prefix for the post author block: By author name"
msgid "by"
msgstr "by"

#: patterns/hidden-post-meta.php:25
msgctxt "Prefix for the post category block: in category name"
msgid "in "
msgstr "in "

#: patterns/hidden-post-navigation.php
msgctxt "Pattern title"
msgid "Post navigation"
msgstr "Post navigation"

#: patterns/hidden-post-navigation.php:9 patterns/hidden-post-navigation.php:10
#: patterns/hidden-posts-heading.php:10
#: patterns/template-index-portfolio.php:16
msgid "Posts"
msgstr "Posts"

#: patterns/hidden-post-navigation.php:11
msgctxt "Label before the title of the previous post. There is a space after the colon."
msgid "Previous: "
msgstr "Previous: "

#: patterns/hidden-post-navigation.php:12
msgctxt "Label before the title of the next post. There is a space after the colon."
msgid "Next: "
msgstr "Next: "

#: patterns/hidden-search.php
msgctxt "Pattern title"
msgid "Search"
msgstr "Search"

#: patterns/hidden-search.php:9 patterns/hidden-sidebar.php:75
msgctxt "search form label"
msgid "Search"
msgstr "Search"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical linen to beige"
msgstr "Vertical linen to beige"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical taupe to beige"
msgstr "Vertical taupe to beige"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical sable to beige"
msgstr "Vertical sable to beige"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical ebony to beige"
msgstr "Vertical ebony to beige"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical ebony to sable"
msgstr "Vertical ebony to sable"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical hard beige to linen"
msgstr "Vertical hard beige to linen"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical hard taupe to beige"
msgstr "Vertical hard taupe to beige"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical hard sable to beige"
msgstr "Vertical hard sable to beige"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical hard ebony to beige"
msgstr "Vertical hard ebony to beige"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical hard sable to taupe"
msgstr "Vertical hard sable to taupe"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical hard ebony to sable"
msgstr "Vertical hard ebony to sable"

#: styles/ember.json styles/maelstrom.json
msgctxt "Color name"
msgid "Contrast / 2"
msgstr "Contrast / 2"

#: styles/ember.json styles/fossil.json styles/ice.json styles/maelstrom.json
#: styles/mint.json styles/onyx.json theme.json
msgctxt "Color name"
msgid "Base / Two"
msgstr "Base / Two"

#: styles/ember.json styles/mint.json
msgctxt "Font family name"
msgid "Instrument Sans"
msgstr "Instrument Sans"

#: styles/ember.json styles/ice.json styles/maelstrom.json styles/mint.json
msgctxt "Font family name"
msgid "Jost"
msgstr "Jost"

#: styles/ember.json styles/fossil.json styles/ice.json styles/maelstrom.json
#: styles/mint.json theme.json
msgctxt "Font family name"
msgid "System Sans-serif"
msgstr "System Sans-serif"

#: styles/ember.json styles/fossil.json styles/ice.json styles/maelstrom.json
#: styles/mint.json theme.json
msgctxt "Font family name"
msgid "System Serif"
msgstr "System Serif"

#: styles/fossil.json
msgctxt "Style variation name"
msgid "Fossil"
msgstr "Fossil"

#: styles/fossil.json styles/ice.json styles/mint.json styles/onyx.json
#: theme.json
msgctxt "Color name"
msgid "Contrast / Two"
msgstr "Contrast / Two"

#: styles/fossil.json styles/ice.json styles/mint.json styles/onyx.json
#: theme.json
msgctxt "Color name"
msgid "Contrast / Three"
msgstr "Contrast / Three"

#: styles/fossil.json styles/maelstrom.json theme.json
msgctxt "Font family name"
msgid "Cardo"
msgstr "Cardo"

#: styles/fossil.json styles/ice.json styles/maelstrom.json theme.json
msgctxt "Font size name"
msgid "Small"
msgstr "Small"

#: styles/fossil.json styles/ice.json styles/maelstrom.json theme.json
msgctxt "Font size name"
msgid "Extra Extra Large"
msgstr "Extra Extra Large"

#: styles/ice.json
msgctxt "Style variation name"
msgid "Ice"
msgstr "Ice"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical azure to ice"
msgstr "Vertical azure to ice"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical slate to ice"
msgstr "Vertical slate to ice"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical ocean to ice"
msgstr "Vertical ocean to ice"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical ink to ice"
msgstr "Vertical ink to ice"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical ocean to slate"
msgstr "Vertical ocean to slate"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical ink to ocean"
msgstr "Vertical ink to ocean"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical hard ice to azure"
msgstr "Vertical hard ice to azure"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical hard slate to ice"
msgstr "Vertical hard slate to ice"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical hard ocean to ice"
msgstr "Vertical hard ocean to ice"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical hard ink to ice"
msgstr "Vertical hard ink to ice"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical hard ocean to slate"
msgstr "Vertical hard ocean to slate"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical hard ink to ocean"
msgstr "Vertical hard ink to ocean"

#: styles/maelstrom.json
msgctxt "Style variation name"
msgid "Maelstrom"
msgstr "Maelstrom"

#: styles/maelstrom.json
msgctxt "Color name"
msgid "Contrast / 3"
msgstr "Contrast / 3"

#: styles/mint.json
msgctxt "Style variation name"
msgid "Mint"
msgstr "Mint"

#: styles/onyx.json
msgctxt "Style variation name"
msgid "Onyx"
msgstr "Onyx"

#: styles/onyx.json
msgctxt "Duotone name"
msgid "Dark gray and white"
msgstr "Dark grey and white"

#: styles/onyx.json
msgctxt "Duotone name"
msgid "Dark gray and walnut"
msgstr "Dark grey and walnut"

#: styles/onyx.json
msgctxt "Duotone name"
msgid "Dark gray and cinnamon"
msgstr "Dark grey and cinnamon"

#: styles/onyx.json
msgctxt "Duotone name"
msgid "Dark gray and olive"
msgstr "Dark grey and olive"

#: styles/onyx.json
msgctxt "Duotone name"
msgid "Dark gray and steel"
msgstr "Dark grey and steel"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft driftwood to dark gray"
msgstr "Vertical soft driftwood to dark grey"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft walnut to dark gray"
msgstr "Vertical soft walnut to dark grey"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft cinnamon to dark gray"
msgstr "Vertical soft cinnamon to dark grey"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft olive to dark gray"
msgstr "Vertical soft olive to dark grey"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft steel to dark gray"
msgstr "Vertical soft steel to dark grey"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft pewter to dark gray"
msgstr "Vertical soft pewter to dark grey"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard beige to dark gray"
msgstr "Vertical hard beige to dark grey"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard walnut to dark gray"
msgstr "Vertical hard walnut to dark grey"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard cinnamon to dark gray"
msgstr "Vertical hard cinnamon to dark grey"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard olive to dark gray"
msgstr "Vertical hard olive to dark grey"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard steel to dark gray"
msgstr "Vertical hard steel to dark grey"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard pewter to dark gray"
msgstr "Vertical hard pewter to dark grey"

#: styles/onyx.json theme.json
msgctxt "Color name"
msgid "Accent"
msgstr "Accent"

#: styles/onyx.json theme.json
msgctxt "Color name"
msgid "Accent / Two"
msgstr "Accent / Two"

#: styles/onyx.json theme.json
msgctxt "Color name"
msgid "Accent / Three"
msgstr "Accent / Three"

#: styles/onyx.json theme.json
msgctxt "Color name"
msgid "Accent / Four"
msgstr "Accent / Four"

#: styles/onyx.json theme.json
msgctxt "Color name"
msgid "Accent / Five"
msgstr "Accent / Five"

#: styles/rust.json
msgctxt "Style variation name"
msgid "Rust"
msgstr "Rust"

#: styles/rust.json
msgctxt "Duotone name"
msgid "Dark rust to beige"
msgstr "Dark rust to beige"

#: styles/rust.json
msgctxt "Gradient name"
msgid "Vertical transparent rust to beige"
msgstr "Vertical transparent rust to beige"

#: styles/rust.json
msgctxt "Gradient name"
msgid "Vertical hard transparent rust to beige"
msgstr "Vertical hard transparent rust to beige"

#: styles/rust.json
msgctxt "Gradient name"
msgid "Vertical rust to beige"
msgstr "Vertical rust to beige"

#: styles/rust.json
msgctxt "Gradient name"
msgid "Vertical hard rust to beige"
msgstr "Vertical hard rust to beige"

#: styles/rust.json
msgctxt "Color name"
msgid "Base / 2"
msgstr "Base / 2"

#: theme.json
msgctxt "Duotone name"
msgid "Black and white"
msgstr "Black and white"

#: theme.json
msgctxt "Duotone name"
msgid "Black and sandstone"
msgstr "Black and sandstone"

#: theme.json
msgctxt "Duotone name"
msgid "Black and rust"
msgstr "Black and rust"

#: theme.json
msgctxt "Duotone name"
msgid "Black and sage"
msgstr "Black and sage"

#: theme.json
msgctxt "Duotone name"
msgid "Black and pastel blue"
msgstr "Black and pastel blue"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft beige to white"
msgstr "Vertical soft beige to white"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft sandstone to white"
msgstr "Vertical soft sandstone to white"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft rust to white"
msgstr "Vertical soft rust to white"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft sage to white"
msgstr "Vertical soft sage to white"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft mint to white"
msgstr "Vertical soft mint to white"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft pewter to white"
msgstr "Vertical soft pewter to white"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard beige to white"
msgstr "Vertical hard beige to white"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard sandstone to white"
msgstr "Vertical hard sandstone to white"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard rust to white"
msgstr "Vertical hard rust to white"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard sage to white"
msgstr "Vertical hard sage to white"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard mint to white"
msgstr "Vertical hard mint to white"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard pewter to white"
msgstr "Vertical hard pewter to white"

#: theme.json
msgctxt "Template part name"
msgid "Sidebar"
msgstr "Sidebar"

#: theme.json
msgctxt "Custom template name"
msgid "Page No Title"
msgstr "Page No Title"

#: theme.json
msgctxt "Custom template name"
msgid "Single with Sidebar"
msgstr "Single with Sidebar"

#: patterns/hidden-search.php:9
msgctxt "search button text"
msgid "Search"
msgstr "Search"

#: patterns/hidden-sidebar.php
msgctxt "Pattern title"
msgid "Sidebar"
msgstr "Sidebar"

#: patterns/hidden-sidebar.php:17
msgid "About the author"
msgstr "About the author"

#: patterns/hidden-sidebar.php:33
msgid "Popular Categories"
msgstr "Popular Categories"

#: patterns/hidden-sidebar.php:49
msgid "Useful Links"
msgstr "Useful Links"

#: patterns/hidden-sidebar.php:53
msgid "Links I found useful and wanted to share."
msgstr "Links I found useful and wanted to share."

#: patterns/hidden-sidebar.php:59
msgid "Latest inflation report"
msgstr "Latest inflation report"

#: patterns/hidden-sidebar.php:60
msgid "Financial apps for families"
msgstr "Financial apps for families"

#: patterns/hidden-sidebar.php:72
msgid "Search the website"
msgstr "Search the website"

#: patterns/hidden-sidebar.php:75
msgctxt "search form placeholder"
msgid "Search..."
msgstr "Search..."

#: patterns/page-about-business.php
msgctxt "Pattern title"
msgid "About"
msgstr "About"

#: patterns/page-home-blogging.php
msgctxt "Pattern title"
msgid "Blogging home"
msgstr "Blogging home"

#: patterns/page-home-business.php
msgctxt "Pattern title"
msgid "Business home"
msgstr "Business home"

#: patterns/page-home-portfolio-gallery.php
msgctxt "Pattern title"
msgid "Portfolio home image gallery"
msgstr "Portfolio home image gallery"

#: patterns/page-home-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio home with post featured images"
msgstr "Portfolio home with post featured images"

#: patterns/page-newsletter-landing.php
msgctxt "Pattern title"
msgid "Newsletter landing"
msgstr "Newsletter landing"

#: patterns/page-newsletter-landing.php:29
msgctxt "sample content for newsletter subscription"
msgid "Subscribe to the newsletter and stay connected with our community"
msgstr "Subscribe to the newsletter and stay connected with our community"

#: patterns/page-newsletter-landing.php:40
msgctxt "Sample content for newsletter subscribe button"
msgid "Sign up"
msgstr "Sign up"

#: patterns/page-portfolio-overview.php
msgctxt "Pattern title"
msgid "Portfolio project overview"
msgstr "Portfolio project overview"

#: patterns/page-rsvp-landing.php
msgctxt "Pattern title"
msgid "RSVP landing"
msgstr "RSVP landing"

#: patterns/page-rsvp-landing.php:14
msgctxt "Name of RSVP landing page pattern"
msgid "RSVP Landing Page"
msgstr "RSVP Landing Page"

#: patterns/page-rsvp-landing.php:49
msgid "Green staircase at Western University, London, Canada"
msgstr "Green staircase at Western University, London, Canada"

#: patterns/posts-1-col.php
msgctxt "Pattern title"
msgid "List of posts, 1 column"
msgstr "List of posts, 1 column"

#: patterns/posts-3-col.php
msgctxt "Pattern title"
msgid "List of posts, 3 columns"
msgstr "List of posts, 3 columns"

#: patterns/posts-grid-2-col.php
msgctxt "Pattern title"
msgid "Grid of posts featuring the first post, 2 columns"
msgstr "Grid of posts featuring the first post, 2 columns"

#: patterns/posts-grid-2-col.php:14 patterns/posts-list.php:14
#: patterns/template-index-blogging.php:16
msgid "Watch, Read, Listen"
msgstr "Watch, Read, Listen"

#: patterns/posts-images-only-3-col.php
msgctxt "Pattern title"
msgid "Posts with featured images only, 3 columns"
msgstr "Posts with featured images only, 3 columns"

#: patterns/posts-images-only-offset-4-col.php
msgctxt "Pattern title"
msgid "Offset posts with featured images only, 4 columns"
msgstr "Offset posts with featured images only, 4 columns"

#: patterns/team-4-col.php
msgctxt "Pattern title"
msgid "Team members, 4 columns"
msgstr "Team members, 4 columns"

#: patterns/team-4-col.php:11
msgctxt "Name of team pattern"
msgid "Team members"
msgstr "Team members"

#: patterns/team-4-col.php:16
msgctxt "Sample heading for the team pattern"
msgid "Meet our team"
msgstr "Meet our team"

#: patterns/team-4-col.php:20
msgctxt "Sample descriptive text of the team pattern"
msgid "Our comprehensive suite of professionals caters to a diverse team, ranging from seasoned architects to renowned engineers."
msgstr "Our comprehensive suite of professionals caters to a diverse team, ranging from seasoned architects to renowned engineers."

#: patterns/team-4-col.php:44
msgctxt "Sample name of a team member"
msgid "Francesca Piovani"
msgstr "Francesca Piovani"

#: patterns/team-4-col.php:49
msgctxt "Sample role of a team member"
msgid "Founder, CEO & Architect"
msgstr "Founder, CEO, and Architect"

#: patterns/team-4-col.php:68
msgctxt "Sample name of a team member"
msgid "Rhye Moore"
msgstr "Rhye Moore"

#: patterns/team-4-col.php:73
msgctxt "Sample role of a team member"
msgid "Engineering Manager"
msgstr "Engineering Manager"

#: patterns/team-4-col.php:92
msgctxt "Sample name of a team member"
msgid "Helga Steiner"
msgstr "Helga Steiner"

#: patterns/team-4-col.php:97
msgctxt "Sample role of a team member"
msgid "Architect"
msgstr "Architect"

#: patterns/team-4-col.php:116
msgctxt "Sample name of a team member"
msgid "Ivan Lawrence"
msgstr "Ivan Lawrence"

#: patterns/team-4-col.php:121
msgctxt "Sample role of a team member"
msgid "Project Manager"
msgstr "Project Manager"

#: patterns/template-archive-blogging.php
msgctxt "Pattern title"
msgid "Blogging archive template"
msgstr "Blogging archive template"

#: patterns/template-archive-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio archive template"
msgstr "Portfolio archive template"

#: patterns/template-home-blogging.php
msgctxt "Pattern title"
msgid "Blogging home template"
msgstr "Blogging home template"

#: patterns/template-home-business.php
msgctxt "Pattern title"
msgid "Business home template"
msgstr "Business home template"

#: patterns/template-home-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio home template with post featured images"
msgstr "Portfolio home template with post featured images"

#: patterns/template-index-blogging.php
msgctxt "Pattern title"
msgid "Blogging index template"
msgstr "Blogging index template"

#: patterns/template-index-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio index template"
msgstr "Portfolio index template"

#: patterns/template-search-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio search template"
msgstr "Portfolio search template"

#: patterns/template-search-blogging.php
msgctxt "Pattern title"
msgid "Blogging search template"
msgstr "Blogging search template"

#: patterns/template-single-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio single post template"
msgstr "Portfolio single post template"

#: patterns/testimonial-centered.php
msgctxt "Pattern title"
msgid "Centered testimonial"
msgstr "Centred testimonial"

#: patterns/testimonial-centered.php:12
msgctxt "Name of testimonial pattern"
msgid "Testimonial"
msgstr "Testimonial"

#: patterns/testimonial-centered.php:18
msgctxt "Testimonial Text or Review Text Got From the Person"
msgid "“Études has saved us thousands of hours of work and has unlocked insights we never thought possible.”"
msgstr "“Études has saved us thousands of hours of work and has unlocked insights we never thought possible.”"

#: patterns/testimonial-centered.php:26
msgctxt "Name of testimonial citation group"
msgid "Testimonial source"
msgstr "Testimonial source"

#: patterns/testimonial-centered.php:35
msgctxt "Name of Person Provided the Testimonial"
msgid "Annie Steiner"
msgstr "Annie Steiner"

#: patterns/testimonial-centered.php:39
msgctxt "Designation of Person Provided Testimonial"
msgid "CEO, Greenprint"
msgstr "CEO, Greenprint"

#: patterns/text-alternating-images.php
msgctxt "Pattern title"
msgid "Text with alternating images"
msgstr "Text with alternating images"

#: patterns/text-alternating-images.php:19
msgctxt "Sample heading content"
msgid "An array of resources"
msgstr "An array of resources"

#: patterns/text-alternating-images.php:23
msgctxt "Sample subheading content"
msgid "Our comprehensive suite of professional services caters to a diverse clientele, ranging from homeowners to commercial developers."
msgstr "Our comprehensive suite of professional services caters to a diverse clientele, ranging from homeowners to commercial developers."

#: patterns/text-alternating-images.php:37
msgctxt "Sample list heading"
msgid "Études Architect App"
msgstr "Études Architect App"

#: patterns/text-alternating-images.php:64
msgid "Tourist taking photo of a building"
msgstr "Tourist taking photo of a building"

#: patterns/text-alternating-images.php:82
msgid "Windows of a building in Nuremberg, Germany"
msgstr "Windows of a building in Nuremberg, Germany"

#: patterns/text-alternating-images.php:91
msgctxt "Sample heading"
msgid "Études Newsletter"
msgstr "Études Newsletter"

#: patterns/text-alternating-images.php:97
msgctxt "Sample list item"
msgid "A world of thought-provoking articles."
msgstr "A world of thought-provoking articles."

#: patterns/text-alternating-images.php:101
msgctxt "Sample list item"
msgid "Case studies that celebrate architecture."
msgstr "Case studies that celebrate architecture."

#: patterns/text-alternating-images.php:105
msgctxt "Sample list item"
msgid "Exclusive access to design insights."
msgstr "Exclusive access to design insights."

#: patterns/text-centered-statement-small.php
msgctxt "Pattern title"
msgid "Centered statement, small"
msgstr "Centred statement, small"

#. Translators: About link placeholder
#: patterns/text-centered-statement-small.php:20
msgid "Money Studies"
msgstr "Money Studies"

#. Translators: About text placeholder
#: patterns/text-centered-statement-small.php:23
msgid "I write about finance, management and economy, my book “%1$s” is out now."
msgstr "I write about finance, management, and economy, my book “%1$s” is out now."

#: patterns/text-centered-statement.php
msgctxt "Pattern title"
msgid "Centered statement"
msgstr "Centred statement"

#: patterns/text-centered-statement.php:21
msgid "<em>Études</em> is not confined to the past—we are passionate about the cutting edge designs shaping our world today."
msgstr "<em>Études</em> is not confined to the past – we are passionate about the cutting edge designs shaping our world today."

#: patterns/text-faq.php
msgctxt "Pattern title"
msgid "FAQ"
msgstr "FAQ"

#: patterns/text-faq.php:12
msgctxt "Name of the FAQ pattern"
msgid "FAQs"
msgstr "FAQs"

#: patterns/text-faq.php:15
msgctxt "Heading of the FAQs"
msgid "FAQs"
msgstr "FAQs"

#: patterns/text-faq.php:24
msgctxt "Question in the FAQ pattern"
msgid "What is your process working in smaller projects?"
msgstr "What is your process working in smaller projects?"

#: patterns/text-faq.php:27 patterns/text-faq.php:38 patterns/text-faq.php:49
#: patterns/text-faq.php:60
msgctxt "Answer in the FAQ pattern"
msgid "Études offers comprehensive consulting, management, design, and research solutions. Our vision is to be at the forefront of architectural innovation, fostering a global community of architects and enthusiasts united by a passion for creating spaces. Every architectural endeavor is an opportunity to shape the future."
msgstr "Études offers comprehensive consulting, management, design, and research solutions. Our vision is to be at the forefront of architectural innovation, fostering a global community of architects and enthusiasts united by a passion for creating spaces. Every architectural endeavour is an opportunity to shape the future."

#: patterns/text-faq.php:46
msgctxt "Question in the FAQ pattern"
msgid "I'd like to get to meet fellow architects, how can I do that?"
msgstr "I'd like to get to meet fellow architects, how can I do that?"

#: patterns/text-faq.php:57
msgctxt "Question in the FAQ pattern"
msgid "Can I apply to be a part of the team or work as a contractor?"
msgstr "Can I apply to be a part of the team or work as a contractor?"

#: patterns/text-feature-grid-3-col.php
msgctxt "Pattern title"
msgid "Feature grid, 3 columns"
msgstr "Feature grid, 3 columns"

#: patterns/text-feature-grid-3-col.php:16
msgctxt "Heading of the features"
msgid "A passion for creating spaces"
msgstr "A passion for creating spaces"

#: patterns/text-feature-grid-3-col.php:24
msgctxt "Sub-heading of the features"
msgid "Our comprehensive suite of professional services caters to a diverse clientele, ranging from homeowners to commercial developers."
msgstr "Our comprehensive suite of professional services caters to a diverse clientele, ranging from homeowners to commercial developers."

#: patterns/text-feature-grid-3-col.php:39
msgctxt "Sample feature heading"
msgid "Renovation and restoration"
msgstr "Renovation and restoration"

#: patterns/text-feature-grid-3-col.php:43
#: patterns/text-feature-grid-3-col.php:55
#: patterns/text-feature-grid-3-col.php:67
#: patterns/text-feature-grid-3-col.php:88
#: patterns/text-feature-grid-3-col.php:100
msgctxt "Sample feature content"
msgid "Experience the fusion of imagination and expertise with Études Architectural Solutions."
msgstr "Experience the fusion of imagination and expertise with Études Architectural Solutions."

#: patterns/text-feature-grid-3-col.php:51
msgctxt "Sample feature heading"
msgid "Continuous Support"
msgstr "Continuous Support"

#: patterns/text-feature-grid-3-col.php:63
msgctxt "Sample feature heading"
msgid "App Access"
msgstr "App Access"

#: patterns/text-feature-grid-3-col.php:84
msgctxt "Sample feature heading"
msgid "Consulting"
msgstr "Consulting"

#: patterns/text-feature-grid-3-col.php:96
msgctxt "Sample feature heading"
msgid "Project Management"
msgstr "Project Management"

#: patterns/text-feature-grid-3-col.php:108
msgctxt "Sample heading"
msgid "Architectural Solutions"
msgstr "Architectural Solutions"

#: patterns/text-feature-grid-3-col.php:112
msgctxt "Sample content"
msgid "Experience the fusion of imagination and expertise with Études Architectural Solutions."
msgstr "Experience the fusion of imagination and expertise with Études Architectural Solutions."

#: patterns/text-project-details.php
msgctxt "Pattern title"
msgid "Project details"
msgstr "Project details"

#: patterns/text-project-details.php:18
msgctxt "Title text for the feature area"
msgid "The revitalized art gallery is set to redefine cultural landscape."
msgstr "The revitalised art gallery is set to redefine cultural landscape."

#: patterns/text-project-details.php:27
msgctxt "Descriptive title for the feature area"
msgid "With meticulous attention to detail and a commitment to excellence, we create spaces that inspire, elevate, and enrich the lives of those who inhabit them."
msgstr "With meticulous attention to detail and a commitment to excellence, we create spaces that inspire, elevate, and enrich the lives of those who inhabit them."

#: patterns/text-title-left-image-right.php
msgctxt "Pattern title"
msgid "Title text and button on left with image on right"
msgstr "Title text and button on left with image on right"

#: patterns/text-title-left-image-right.php:21
msgctxt "Headline for the About pattern"
msgid "Études offers comprehensive consulting, management, design, and research solutions. Every architectural endeavor is an opportunity to shape the future."
msgstr "Études offers comprehensive consulting, management, design, and research solutions. Every architectural endeavour is an opportunity to shape the future."

#: patterns/text-title-left-image-right.php:28
msgctxt "Description for the About pattern"
msgid "Leaving an indelible mark on the landscape of tomorrow."
msgstr "Leaving an indelible mark on the landscape of tomorrow."

#: patterns/text-project-details.php:35 patterns/text-project-details.php:43
msgctxt "Descriptive text for the feature area"
msgid "The revitalized Art Gallery is set to redefine the cultural landscape of Toronto, serving as a nexus of artistic expression, community engagement, and architectural marvel. The expansion and renovation project pay homage to the Art Gallery's rich history while embracing the future, ensuring that the gallery remains a beacon of inspiration."
msgstr "The revitalised Art Gallery is set to redefine the cultural landscape of Toronto, serving as a nexus of artistic expression, community engagement, and architectural marvel. The expansion and renovation project pay homage to the Art Gallery's rich history while embracing the future, ensuring that the gallery remains a beacon of inspiration."

#: patterns/text-title-left-image-right.php:35
msgctxt "Call to Action button text"
msgid "About us"
msgstr "About us"

#: styles/ember.json
msgctxt "Style variation name"
msgid "Ember"
msgstr "Ember"

#: styles/ember.json
msgctxt "Duotone name"
msgid "Orange and white"
msgstr "Orange and white"

#: theme.json
msgctxt "Custom template name"
msgid "Page with Wide Image"
msgstr "Page with Wide Image"

#: theme.json
msgctxt "Custom template name"
msgid "Page with Sidebar"
msgstr "Page with Sidebar"

#. Author of the theme
#: style.css
#, gp-priority: low
msgid "the WordPress team"
msgstr "the WordPress team"

#. Theme URI of the theme
#: style.css
#, gp-priority: low
msgid "https://wordpress.org/themes/twentytwentyfour/"
msgstr "https://en-gb.wordpress.org/themes/twentytwentyfour/"
