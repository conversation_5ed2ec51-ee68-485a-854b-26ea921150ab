<?php
/**
 * Plugin Name: Site Chat System
 * Plugin URI: https://yoursite.com/site-chat-system
 * Description: A real-time chat system for registered users with a floating chat widget in the bottom right corner.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://yoursite.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: site-chat-system
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.3
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('SITE_CHAT_VERSION', '1.0.0');
define('SITE_CHAT_PLUGIN_FILE', __FILE__);
define('SITE_CHAT_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('SITE_CHAT_PLUGIN_URL', plugin_dir_url(__FILE__));
define('SITE_CHAT_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main plugin class
 */
class Site_Chat_System {
    
    /**
     * Single instance of the plugin
     */
    private static $instance = null;
    
    /**
     * Plugin components
     */
    public $database;
    public $chat_manager;
    public $admin;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->load_dependencies();
        $this->init_components();
    }
    
    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        register_uninstall_hook(__FILE__, array('Site_Chat_System', 'uninstall'));
        
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_footer', array($this, 'render_chat_widget'));
        add_action('plugins_loaded', array($this, 'load_textdomain'));
    }
    
    /**
     * Load plugin dependencies
     */
    private function load_dependencies() {
        require_once SITE_CHAT_PLUGIN_DIR . 'includes/class-database.php';
        require_once SITE_CHAT_PLUGIN_DIR . 'includes/class-chat-manager.php';
        require_once SITE_CHAT_PLUGIN_DIR . 'includes/class-admin.php';
    }
    
    /**
     * Initialize plugin components
     */
    private function init_components() {
        $this->database = new Site_Chat_Database();
        $this->chat_manager = new Site_Chat_Manager();
        $this->admin = new Site_Chat_Admin();
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        $this->chat_manager->init();
        $this->admin->init();
    }
    
    /**
     * Load plugin textdomain
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'site-chat-system',
            false,
            dirname(SITE_CHAT_PLUGIN_BASENAME) . '/languages'
        );
    }
    
    /**
     * Enqueue frontend scripts and styles
     */
    public function enqueue_scripts() {
        // Only load for logged-in users
        if (!is_user_logged_in()) {
            return;
        }
        
        wp_enqueue_style(
            'site-chat-frontend',
            SITE_CHAT_PLUGIN_URL . 'assets/css/frontend.css',
            array(),
            SITE_CHAT_VERSION
        );
        
        wp_enqueue_script(
            'site-chat-frontend',
            SITE_CHAT_PLUGIN_URL . 'assets/js/frontend.js',
            array('jquery'),
            SITE_CHAT_VERSION,
            true
        );
        
        wp_localize_script('site-chat-frontend', 'siteChatAjax', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('site_chat_nonce'),
            'currentUserId' => get_current_user_id(),
            'currentUserName' => wp_get_current_user()->display_name,
            'currentUserAvatar' => get_avatar_url(get_current_user_id(), array('size' => 32)),
            'strings' => array(
                'typeMessage' => __('Type a message...', 'site-chat-system'),
                'send' => __('Send', 'site-chat-system'),
                'online' => __('Online', 'site-chat-system'),
                'offline' => __('Offline', 'site-chat-system'),
                'connecting' => __('Connecting...', 'site-chat-system'),
                'error' => __('An error occurred. Please try again.', 'site-chat-system'),
            )
        ));
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'site-chat') === false) {
            return;
        }
        
        wp_enqueue_style(
            'site-chat-admin',
            SITE_CHAT_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            SITE_CHAT_VERSION
        );
        
        wp_enqueue_script(
            'site-chat-admin',
            SITE_CHAT_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            SITE_CHAT_VERSION,
            true
        );
    }
    
    /**
     * Render chat widget in footer
     */
    public function render_chat_widget() {
        // Only show for logged-in users
        if (!is_user_logged_in()) {
            return;
        }
        
        // Don't show on admin pages
        if (is_admin()) {
            return;
        }
        
        // Check if chat is enabled
        $options = get_option('site_chat_options', array());
        if (!($options['enable_chat'] ?? true)) {
            return;
        }
        
        include SITE_CHAT_PLUGIN_DIR . 'templates/chat-widget.php';
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables
        $this->database->create_tables();
        
        // Set default options
        $this->set_default_options();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin uninstall
     */
    public static function uninstall() {
        // Remove database tables
        $database = new Site_Chat_Database();
        $database->drop_tables();
        
        // Remove options
        delete_option('site_chat_options');
        delete_option('site_chat_version');
    }
    
    /**
     * Set default plugin options
     */
    private function set_default_options() {
        $default_options = array(
            'enable_chat' => true,
            'max_message_length' => 500,
            'message_history_limit' => 50,
            'auto_delete_messages' => false,
            'delete_messages_after_days' => 30,
            'enable_emojis' => true,
            'enable_file_sharing' => false,
            'allowed_file_types' => array('jpg', 'jpeg', 'png', 'gif'),
            'max_file_size' => 2048, // KB
            'chat_position' => 'bottom-right',
            'chat_theme' => 'default',
            'enable_sound_notifications' => true,
            'enable_desktop_notifications' => true,
            'refresh_interval' => 3000, // milliseconds
        );
        
        add_option('site_chat_options', $default_options);
        add_option('site_chat_version', SITE_CHAT_VERSION);
    }
}

/**
 * Initialize the plugin
 */
function site_chat_system() {
    return Site_Chat_System::get_instance();
}

// Start the plugin
site_chat_system();
