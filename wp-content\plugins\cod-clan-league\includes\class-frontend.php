<?php
/**
 * Frontend display and shortcodes
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class COD_Clan_League_Frontend {
    
    /**
     * Initialize frontend
     */
    public function init() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('init', array($this, 'register_shortcodes'));
        add_action('wp_ajax_cod_frontend_action', array($this, 'handle_ajax_actions'));
        add_action('wp_ajax_nopriv_cod_frontend_action', array($this, 'handle_ajax_actions'));
    }
    
    /**
     * Enqueue frontend scripts and styles
     */
    public function enqueue_scripts() {
        wp_enqueue_style(
            'cod-clan-league-frontend',
            COD_CLAN_LEAGUE_PLUGIN_URL . 'assets/css/frontend.css',
            array(),
            COD_CLAN_LEAGUE_VERSION
        );
        
        wp_enqueue_script(
            'cod-clan-league-frontend',
            COD_CLAN_LEAGUE_PLUGIN_URL . 'assets/js/frontend.js',
            array('jquery'),
            COD_CLAN_LEAGUE_VERSION,
            true
        );
    }
    
    /**
     * Register shortcodes
     */
    public function register_shortcodes() {
        add_shortcode('cod_league_table', array($this, 'shortcode_league_table'));
        add_shortcode('cod_fixtures', array($this, 'shortcode_fixtures'));
        add_shortcode('cod_results', array($this, 'shortcode_results'));
        add_shortcode('cod_clan_profile', array($this, 'shortcode_clan_profile'));
        add_shortcode('cod_clan_list', array($this, 'shortcode_clan_list'));
        add_shortcode('cod_season_info', array($this, 'shortcode_season_info'));
        add_shortcode('cod_match_report_form', array($this, 'shortcode_match_report_form'));
    }
    
    /**
     * League table shortcode
     */
    public function shortcode_league_table($atts) {
        $atts = shortcode_atts(array(
            'season_id' => 0,
            'limit' => 0,
            'show_logo' => 'true',
            'show_stats' => 'true',
        ), $atts);
        
        $season_id = intval($atts['season_id']);
        if (!$season_id) {
            return '<p>' . __('Please specify a season ID.', 'cod-clan-league') . '</p>';
        }
        
        $league_manager = new COD_Clan_League_League_Manager();
        $table = $league_manager->get_league_table($season_id);
        
        if (empty($table)) {
            return '<p>' . __('No teams found for this season.', 'cod-clan-league') . '</p>';
        }
        
        if ($atts['limit'] > 0) {
            $table = array_slice($table, 0, intval($atts['limit']));
        }
        
        ob_start();
        ?>
        <div class="cod-league-table">
            <table class="cod-table">
                <thead>
                    <tr>
                        <th><?php _e('Position', 'cod-clan-league'); ?></th>
                        <?php if ($atts['show_logo'] === 'true'): ?>
                        <th><?php _e('Logo', 'cod-clan-league'); ?></th>
                        <?php endif; ?>
                        <th><?php _e('Team', 'cod-clan-league'); ?></th>
                        <?php if ($atts['show_stats'] === 'true'): ?>
                        <th><?php _e('Played', 'cod-clan-league'); ?></th>
                        <th><?php _e('Won', 'cod-clan-league'); ?></th>
                        <th><?php _e('Lost', 'cod-clan-league'); ?></th>
                        <?php endif; ?>
                        <th><?php _e('Points', 'cod-clan-league'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($table as $position => $team): ?>
                    <tr>
                        <td><?php echo $position + 1; ?></td>
                        <?php if ($atts['show_logo'] === 'true'): ?>
                        <td>
                            <?php if ($team->logo_url): ?>
                            <img src="<?php echo esc_url($team->logo_url); ?>" alt="<?php echo esc_attr($team->clan_name); ?>" class="cod-clan-logo">
                            <?php endif; ?>
                        </td>
                        <?php endif; ?>
                        <td>
                            <strong><?php echo esc_html($team->clan_name); ?></strong>
                            <?php if ($team->clan_tag): ?>
                            <span class="cod-clan-tag">[<?php echo esc_html($team->clan_tag); ?>]</span>
                            <?php endif; ?>
                        </td>
                        <?php if ($atts['show_stats'] === 'true'): ?>
                        <td><?php echo intval($team->matches_played); ?></td>
                        <td><?php echo intval($team->matches_won); ?></td>
                        <td><?php echo intval($team->matches_lost); ?></td>
                        <?php endif; ?>
                        <td><strong><?php echo intval($team->points); ?></strong></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Fixtures shortcode
     */
    public function shortcode_fixtures($atts) {
        $atts = shortcode_atts(array(
            'season_id' => 0,
            'status' => 'scheduled',
            'limit' => 10,
            'show_date' => 'true',
        ), $atts);
        
        $season_id = intval($atts['season_id']);
        if (!$season_id) {
            return '<p>' . __('Please specify a season ID.', 'cod-clan-league') . '</p>';
        }
        
        $match_manager = new COD_Clan_League_Match_Manager();
        $matches = $match_manager->get_season_matches($season_id, $atts['status']);
        
        if (empty($matches)) {
            return '<p>' . __('No fixtures found.', 'cod-clan-league') . '</p>';
        }
        
        if ($atts['limit'] > 0) {
            $matches = array_slice($matches, 0, intval($atts['limit']));
        }
        
        ob_start();
        ?>
        <div class="cod-fixtures">
            <?php foreach ($matches as $match): ?>
            <div class="cod-fixture">
                <?php if ($atts['show_date'] === 'true' && $match->scheduled_date): ?>
                <div class="cod-match-date">
                    <?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($match->scheduled_date)); ?>
                </div>
                <?php endif; ?>
                <div class="cod-match-teams">
                    <span class="cod-home-team">
                        <?php echo esc_html($match->home_clan_name); ?>
                        <?php if ($match->home_clan_tag): ?>
                        <span class="cod-clan-tag">[<?php echo esc_html($match->home_clan_tag); ?>]</span>
                        <?php endif; ?>
                    </span>
                    <span class="cod-vs"><?php _e('vs', 'cod-clan-league'); ?></span>
                    <span class="cod-away-team">
                        <?php echo esc_html($match->away_clan_name); ?>
                        <?php if ($match->away_clan_tag): ?>
                        <span class="cod-clan-tag">[<?php echo esc_html($match->away_clan_tag); ?>]</span>
                        <?php endif; ?>
                    </span>
                </div>
                <?php if ($match->status === 'pending_approval'): ?>
                <div class="cod-match-status pending">
                    <?php _e('Result pending approval', 'cod-clan-league'); ?>
                </div>
                <?php endif; ?>
            </div>
            <?php endforeach; ?>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Results shortcode
     */
    public function shortcode_results($atts) {
        $atts = shortcode_atts(array(
            'season_id' => 0,
            'limit' => 10,
            'show_date' => 'true',
            'show_score' => 'true',
        ), $atts);
        
        $season_id = intval($atts['season_id']);
        if (!$season_id) {
            return '<p>' . __('Please specify a season ID.', 'cod-clan-league') . '</p>';
        }
        
        $match_manager = new COD_Clan_League_Match_Manager();
        $matches = $match_manager->get_season_matches($season_id, 'completed');
        
        if (empty($matches)) {
            return '<p>' . __('No results found.', 'cod-clan-league') . '</p>';
        }
        
        // Sort by completed date, most recent first
        usort($matches, function($a, $b) {
            return strtotime($b->completed_date) - strtotime($a->completed_date);
        });
        
        if ($atts['limit'] > 0) {
            $matches = array_slice($matches, 0, intval($atts['limit']));
        }
        
        ob_start();
        ?>
        <div class="cod-results">
            <?php foreach ($matches as $match): ?>
            <div class="cod-result">
                <?php if ($atts['show_date'] === 'true' && $match->completed_date): ?>
                <div class="cod-match-date">
                    <?php echo date_i18n(get_option('date_format'), strtotime($match->completed_date)); ?>
                </div>
                <?php endif; ?>
                <div class="cod-match-result">
                    <span class="cod-home-team <?php echo ($match->winner_team_id == $match->home_team_id) ? 'winner' : ''; ?>">
                        <?php echo esc_html($match->home_clan_name); ?>
                        <?php if ($match->home_clan_tag): ?>
                        <span class="cod-clan-tag">[<?php echo esc_html($match->home_clan_tag); ?>]</span>
                        <?php endif; ?>
                    </span>
                    <?php if ($atts['show_score'] === 'true'): ?>
                    <span class="cod-score">
                        <?php echo intval($match->home_score); ?> - <?php echo intval($match->away_score); ?>
                    </span>
                    <?php endif; ?>
                    <span class="cod-away-team <?php echo ($match->winner_team_id == $match->away_team_id) ? 'winner' : ''; ?>">
                        <?php echo esc_html($match->away_clan_name); ?>
                        <?php if ($match->away_clan_tag): ?>
                        <span class="cod-clan-tag">[<?php echo esc_html($match->away_clan_tag); ?>]</span>
                        <?php endif; ?>
                    </span>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Clan profile shortcode
     */
    public function shortcode_clan_profile($atts) {
        $atts = shortcode_atts(array(
            'clan_id' => 0,
            'show_members' => 'true',
            'show_matches' => 'true',
            'match_limit' => 5,
        ), $atts);
        
        $clan_id = intval($atts['clan_id']);
        if (!$clan_id) {
            return '<p>' . __('Please specify a clan ID.', 'cod-clan-league') . '</p>';
        }
        
        $clan_manager = new COD_Clan_League_Clan_Manager();
        $clan = $clan_manager->get_clan($clan_id);
        
        if (!$clan) {
            return '<p>' . __('Clan not found.', 'cod-clan-league') . '</p>';
        }
        
        ob_start();
        ?>
        <div class="cod-clan-profile">
            <div class="cod-clan-header">
                <?php if ($clan->logo_url): ?>
                <img src="<?php echo esc_url($clan->logo_url); ?>" alt="<?php echo esc_attr($clan->name); ?>" class="cod-clan-logo-large">
                <?php endif; ?>
                <h2><?php echo esc_html($clan->name); ?>
                    <?php if ($clan->tag): ?>
                    <span class="cod-clan-tag">[<?php echo esc_html($clan->tag); ?>]</span>
                    <?php endif; ?>
                </h2>
                <?php if ($clan->description): ?>
                <p class="cod-clan-description"><?php echo wp_kses_post($clan->description); ?></p>
                <?php endif; ?>
            </div>
            
            <?php if ($atts['show_members'] === 'true'): ?>
            <div class="cod-clan-members">
                <h3><?php _e('Members', 'cod-clan-league'); ?></h3>
                <?php
                $members = $clan_manager->get_clan_members($clan_id);
                if ($members):
                ?>
                <ul class="cod-member-list">
                    <?php foreach ($members as $member): ?>
                    <li class="cod-member <?php echo esc_attr($member->role); ?>">
                        <?php echo esc_html($member->display_name); ?>
                        <?php if ($member->role === 'captain'): ?>
                        <span class="cod-role-badge"><?php _e('Captain', 'cod-clan-league'); ?></span>
                        <?php endif; ?>
                    </li>
                    <?php endforeach; ?>
                </ul>
                <?php else: ?>
                <p><?php _e('No members found.', 'cod-clan-league'); ?></p>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            
            <?php if ($atts['show_matches'] === 'true'): ?>
            <div class="cod-clan-matches">
                <h3><?php _e('Recent Matches', 'cod-clan-league'); ?></h3>
                <?php
                // Get recent matches for this clan
                global $wpdb;
                $matches_table = $wpdb->prefix . 'cod_matches';
                $season_teams_table = $wpdb->prefix . 'cod_season_teams';
                
                $recent_matches = $wpdb->get_results($wpdb->prepare(
                    "SELECT m.*, 
                            ht.clan_id as home_clan_id, 
                            at.clan_id as away_clan_id,
                            hc.name as home_clan_name, hc.tag as home_clan_tag,
                            ac.name as away_clan_name, ac.tag as away_clan_tag
                     FROM $matches_table m
                     INNER JOIN $season_teams_table ht ON m.home_team_id = ht.id
                     INNER JOIN $season_teams_table at ON m.away_team_id = at.id
                     INNER JOIN {$wpdb->prefix}cod_clans hc ON ht.clan_id = hc.id
                     INNER JOIN {$wpdb->prefix}cod_clans ac ON at.clan_id = ac.id
                     WHERE (ht.clan_id = %d OR at.clan_id = %d) AND m.status = 'completed'
                     ORDER BY m.completed_date DESC
                     LIMIT %d",
                    $clan_id, $clan_id, intval($atts['match_limit'])
                ));
                
                if ($recent_matches):
                ?>
                <div class="cod-match-history">
                    <?php foreach ($recent_matches as $match): ?>
                    <div class="cod-match-result">
                        <span class="cod-home-team <?php echo ($match->winner_team_id == $match->home_team_id) ? 'winner' : ''; ?>">
                            <?php echo esc_html($match->home_clan_name); ?>
                            <?php if ($match->home_clan_tag): ?>
                            <span class="cod-clan-tag">[<?php echo esc_html($match->home_clan_tag); ?>]</span>
                            <?php endif; ?>
                        </span>
                        <span class="cod-score">
                            <?php echo intval($match->home_score); ?> - <?php echo intval($match->away_score); ?>
                        </span>
                        <span class="cod-away-team <?php echo ($match->winner_team_id == $match->away_team_id) ? 'winner' : ''; ?>">
                            <?php echo esc_html($match->away_clan_name); ?>
                            <?php if ($match->away_clan_tag): ?>
                            <span class="cod-clan-tag">[<?php echo esc_html($match->away_clan_tag); ?>]</span>
                            <?php endif; ?>
                        </span>
                        <div class="cod-match-date">
                            <?php echo date_i18n(get_option('date_format'), strtotime($match->completed_date)); ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <p><?php _e('No matches found.', 'cod-clan-league'); ?></p>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Clan list shortcode
     */
    public function shortcode_clan_list($atts) {
        $atts = shortcode_atts(array(
            'status' => 'active',
            'limit' => 0,
            'show_logo' => 'true',
            'show_members' => 'true',
        ), $atts);
        
        $clan_manager = new COD_Clan_League_Clan_Manager();
        $clans = $clan_manager->get_clans(array(
            'status' => $atts['status'],
            'limit' => intval($atts['limit']),
        ));
        
        if (empty($clans)) {
            return '<p>' . __('No clans found.', 'cod-clan-league') . '</p>';
        }
        
        ob_start();
        ?>
        <div class="cod-clan-list">
            <?php foreach ($clans as $clan): ?>
            <div class="cod-clan-item">
                <?php if ($atts['show_logo'] === 'true' && $clan->logo_url): ?>
                <img src="<?php echo esc_url($clan->logo_url); ?>" alt="<?php echo esc_attr($clan->name); ?>" class="cod-clan-logo">
                <?php endif; ?>
                <div class="cod-clan-info">
                    <h3><?php echo esc_html($clan->name); ?>
                        <?php if ($clan->tag): ?>
                        <span class="cod-clan-tag">[<?php echo esc_html($clan->tag); ?>]</span>
                        <?php endif; ?>
                    </h3>
                    <?php if ($clan->description): ?>
                    <p class="cod-clan-description"><?php echo wp_kses_post($clan->description); ?></p>
                    <?php endif; ?>
                    <?php if ($atts['show_members'] === 'true'): ?>
                    <?php
                    global $wpdb;
                    $member_count = $wpdb->get_var($wpdb->prepare(
                        "SELECT COUNT(*) FROM {$wpdb->prefix}cod_clan_members WHERE clan_id = %d AND status = 'active'",
                        $clan->id
                    ));
                    ?>
                    <p class="cod-member-count"><?php printf(__('%d members', 'cod-clan-league'), $member_count); ?></p>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Season info shortcode
     */
    public function shortcode_season_info($atts) {
        $atts = shortcode_atts(array(
            'season_id' => 0,
            'show_teams' => 'true',
            'show_dates' => 'true',
        ), $atts);

        $season_id = intval($atts['season_id']);
        if (!$season_id) {
            return '<p>' . __('Please specify a season ID.', 'cod-clan-league') . '</p>';
        }

        $league_manager = new COD_Clan_League_League_Manager();
        $season = $league_manager->get_season($season_id);

        if (!$season) {
            return '<p>' . __('Season not found.', 'cod-clan-league') . '</p>';
        }

        ob_start();
        ?>
        <div class="cod-season-info">
            <h2><?php echo esc_html($season->name); ?></h2>
            <?php if ($season->description): ?>
            <p class="cod-season-description"><?php echo wp_kses_post($season->description); ?></p>
            <?php endif; ?>

            <?php if ($atts['show_dates'] === 'true'): ?>
            <div class="cod-season-dates">
                <p><strong><?php _e('Start Date:', 'cod-clan-league'); ?></strong> <?php echo date_i18n(get_option('date_format'), strtotime($season->start_date)); ?></p>
                <p><strong><?php _e('End Date:', 'cod-clan-league'); ?></strong> <?php echo date_i18n(get_option('date_format'), strtotime($season->end_date)); ?></p>
                <p><strong><?php _e('Status:', 'cod-clan-league'); ?></strong> <?php echo esc_html(ucfirst($season->status)); ?></p>
            </div>
            <?php endif; ?>

            <?php if ($atts['show_teams'] === 'true'): ?>
            <div class="cod-season-teams">
                <h3><?php _e('Participating Teams', 'cod-clan-league'); ?></h3>
                <?php
                $teams = $league_manager->get_season_teams($season_id, 'approved');
                if ($teams):
                ?>
                <ul class="cod-team-list">
                    <?php foreach ($teams as $team): ?>
                    <li class="cod-team-item">
                        <?php if ($team->logo_url): ?>
                        <img src="<?php echo esc_url($team->logo_url); ?>" alt="<?php echo esc_attr($team->clan_name); ?>" class="cod-clan-logo-small">
                        <?php endif; ?>
                        <?php echo esc_html($team->clan_name); ?>
                        <?php if ($team->clan_tag): ?>
                        <span class="cod-clan-tag">[<?php echo esc_html($team->clan_tag); ?>]</span>
                        <?php endif; ?>
                    </li>
                    <?php endforeach; ?>
                </ul>
                <?php else: ?>
                <p><?php _e('No teams registered yet.', 'cod-clan-league'); ?></p>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Match report form shortcode
     */
    public function shortcode_match_report_form($atts) {
        $atts = shortcode_atts(array(
            'match_id' => 0,
        ), $atts);

        $match_id = intval($atts['match_id']);
        if (!$match_id) {
            return '<p>' . __('Please specify a match ID.', 'cod-clan-league') . '</p>';
        }

        if (!is_user_logged_in()) {
            return '<p>' . __('You must be logged in to report match results.', 'cod-clan-league') . '</p>';
        }

        $match_manager = new COD_Clan_League_Match_Manager();
        $match = $match_manager->get_match($match_id);

        if (!$match) {
            return '<p>' . __('Match not found.', 'cod-clan-league') . '</p>';
        }

        // Check permissions
        $user_roles = new COD_Clan_League_User_Roles();
        if (!$user_roles->user_can_report_match(get_current_user_id(), $match_id) && !current_user_can('manage_cod_matches')) {
            return '<p>' . __('You do not have permission to report this match.', 'cod-clan-league') . '</p>';
        }

        if ($match->status !== 'scheduled') {
            return '<p>' . __('This match has already been reported or is not scheduled.', 'cod-clan-league') . '</p>';
        }

        // Get team names
        global $wpdb;
        $season_teams_table = $wpdb->prefix . 'cod_season_teams';
        $clans_table = $wpdb->prefix . 'cod_clans';

        $teams = $wpdb->get_results($wpdb->prepare(
            "SELECT st.id, st.clan_id, c.name as clan_name, c.tag as clan_tag
             FROM $season_teams_table st
             INNER JOIN $clans_table c ON st.clan_id = c.id
             WHERE st.id IN (%d, %d)",
            $match->home_team_id, $match->away_team_id
        ));

        $home_team = null;
        $away_team = null;
        foreach ($teams as $team) {
            if ($team->id == $match->home_team_id) {
                $home_team = $team;
            } elseif ($team->id == $match->away_team_id) {
                $away_team = $team;
            }
        }

        ob_start();
        ?>
        <div class="cod-match-report-form">
            <h3><?php _e('Report Match Result', 'cod-clan-league'); ?></h3>
            <div class="cod-match-info">
                <p><strong><?php _e('Match:', 'cod-clan-league'); ?></strong>
                   <?php echo esc_html($home_team->clan_name); ?> vs <?php echo esc_html($away_team->clan_name); ?>
                </p>
                <?php if ($match->scheduled_date): ?>
                <p><strong><?php _e('Scheduled:', 'cod-clan-league'); ?></strong>
                   <?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($match->scheduled_date)); ?>
                </p>
                <?php endif; ?>
            </div>

            <form id="cod-match-report-form" class="cod-form">
                <?php wp_nonce_field('cod_clan_league_nonce', 'nonce'); ?>
                <input type="hidden" name="match_id" value="<?php echo $match_id; ?>">

                <div class="cod-form-row">
                    <div class="cod-form-group">
                        <label for="home_score"><?php echo esc_html($home_team->clan_name); ?> <?php _e('Score', 'cod-clan-league'); ?></label>
                        <input type="number" id="home_score" name="home_score" min="0" required>
                    </div>
                    <div class="cod-form-group">
                        <label for="away_score"><?php echo esc_html($away_team->clan_name); ?> <?php _e('Score', 'cod-clan-league'); ?></label>
                        <input type="number" id="away_score" name="away_score" min="0" required>
                    </div>
                </div>

                <div class="cod-form-group">
                    <label for="match_notes"><?php _e('Match Notes (Optional)', 'cod-clan-league'); ?></label>
                    <textarea id="match_notes" name="notes" rows="3"></textarea>
                </div>

                <div class="cod-form-group">
                    <label for="match_screenshot"><?php _e('Screenshot (Optional)', 'cod-clan-league'); ?></label>
                    <input type="file" id="match_screenshot" name="screenshot" accept="image/*">
                    <small><?php _e('Upload a screenshot as proof of the result (JPEG, PNG, GIF - max 5MB)', 'cod-clan-league'); ?></small>
                </div>

                <div class="cod-form-actions">
                    <button type="submit" class="cod-btn cod-btn-primary"><?php _e('Submit Result', 'cod-clan-league'); ?></button>
                </div>
            </form>

            <div id="cod-form-messages"></div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            $('#cod-match-report-form').on('submit', function(e) {
                e.preventDefault();

                var formData = new FormData(this);
                formData.append('action', 'cod_report_match_result');

                $.ajax({
                    url: codClanLeague.ajaxUrl,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            $('#cod-form-messages').html('<div class="cod-message cod-success">' + response.data + '</div>');
                            $('#cod-match-report-form')[0].reset();
                        } else {
                            $('#cod-form-messages').html('<div class="cod-message cod-error">' + response.data + '</div>');
                        }
                    },
                    error: function() {
                        $('#cod-form-messages').html('<div class="cod-message cod-error">' + codClanLeague.strings.error + '</div>');
                    }
                });
            });
        });
        </script>
        <?php
        return ob_get_clean();
    }

    /**
     * Handle AJAX actions
     */
    public function handle_ajax_actions() {
        // This method can be used for additional frontend AJAX actions
        // Most AJAX actions are handled in their respective manager classes
    }
}
