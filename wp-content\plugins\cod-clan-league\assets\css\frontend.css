/**
 * COD Clan League Frontend Styles
 */

/* General Styles */
.cod-league-table,
.cod-fixtures,
.cod-results,
.cod-clan-profile,
.cod-clan-list,
.cod-season-info,
.cod-match-report-form {
    margin: 20px 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* Table Styles */
.cod-table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.cod-table th,
.cod-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.cod-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 0.5px;
}

.cod-table tr:hover {
    background-color: #f5f5f5;
}

.cod-table tr:nth-child(even) {
    background-color: #fafafa;
}

/* Clan Logo */
.cod-clan-logo {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    object-fit: cover;
}

.cod-clan-logo-large {
    width: 64px;
    height: 64px;
    border-radius: 8px;
    object-fit: cover;
}

.cod-clan-logo-small {
    width: 24px;
    height: 24px;
    border-radius: 3px;
    object-fit: cover;
}

/* Clan Tag */
.cod-clan-tag {
    display: inline-block;
    background: #007cba;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
    margin-left: 5px;
}

/* Status Indicators */
.cod-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.cod-status-active,
.cod-status-completed,
.cod-status-approved,
.cod-status-success {
    background: #d4edda;
    color: #155724;
}

.cod-status-pending,
.cod-status-upcoming,
.cod-status-warning {
    background: #fff3cd;
    color: #856404;
}

.cod-status-inactive,
.cod-status-cancelled,
.cod-status-declined,
.cod-status-danger {
    background: #f8d7da;
    color: #721c24;
}

/* Fixtures */
.cod-fixture {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.cod-match-date {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.cod-match-teams {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 600;
}

.cod-vs {
    color: #999;
    font-weight: 400;
    font-size: 14px;
}

.cod-home-team,
.cod-away-team {
    flex: 1;
    text-align: center;
}

.cod-home-team {
    text-align: left;
}

.cod-away-team {
    text-align: right;
}

/* Results */
.cod-result {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.cod-match-result {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 600;
}

.cod-score {
    background: #f8f9fa;
    padding: 8px 12px;
    border-radius: 6px;
    font-weight: 700;
    color: #333;
    min-width: 60px;
    text-align: center;
}

.cod-home-team.winner,
.cod-away-team.winner {
    color: #28a745;
}

/* Clan Profile */
.cod-clan-header {
    text-align: center;
    padding: 30px 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 30px;
}

.cod-clan-header h2 {
    margin: 15px 0 10px;
    color: #333;
}

.cod-clan-description {
    color: #666;
    font-size: 16px;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

.cod-clan-members,
.cod-clan-matches {
    margin: 30px 0;
}

.cod-clan-members h3,
.cod-clan-matches h3 {
    border-bottom: 2px solid #007cba;
    padding-bottom: 10px;
    margin-bottom: 20px;
    color: #333;
}

.cod-member-list {
    list-style: none;
    padding: 0;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
}

.cod-member {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.cod-member.captain {
    background: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.cod-role-badge {
    background: #007cba;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 600;
}

/* Clan List */
.cod-clan-item {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
    gap: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.cod-clan-info h3 {
    margin: 0 0 10px;
    color: #333;
}

.cod-member-count {
    color: #666;
    font-size: 14px;
    margin: 5px 0 0;
}

/* Season Info */
.cod-season-dates {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
}

.cod-season-dates p {
    margin: 5px 0;
}

.cod-team-list {
    list-style: none;
    padding: 0;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 10px;
}

.cod-team-item {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Forms */
.cod-form {
    background: #fff;
    padding: 30px;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.cod-form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.cod-form-group {
    margin-bottom: 20px;
}

.cod-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.cod-form-group input,
.cod-form-group textarea,
.cod-form-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.cod-form-group input:focus,
.cod-form-group textarea:focus,
.cod-form-group select:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.1);
}

.cod-form-group small {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 12px;
}

.cod-form-actions {
    text-align: right;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

/* Buttons */
.cod-btn {
    display: inline-block;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.cod-btn-primary {
    background: #007cba;
    color: white;
}

.cod-btn-primary:hover {
    background: #005a87;
    color: white;
}

.cod-btn-secondary {
    background: #6c757d;
    color: white;
}

.cod-btn-secondary:hover {
    background: #545b62;
    color: white;
}

/* Messages */
.cod-message {
    padding: 12px 16px;
    border-radius: 4px;
    margin: 15px 0;
    font-weight: 500;
}

.cod-message.cod-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.cod-message.cod-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.cod-message.cod-warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* Responsive Design */
@media (max-width: 768px) {
    .cod-form-row {
        grid-template-columns: 1fr;
    }
    
    .cod-match-teams {
        flex-direction: column;
        gap: 10px;
    }
    
    .cod-home-team,
    .cod-away-team {
        text-align: center;
    }
    
    .cod-clan-item {
        flex-direction: column;
        text-align: center;
    }
    
    .cod-table {
        font-size: 14px;
    }
    
    .cod-table th,
    .cod-table td {
        padding: 8px 10px;
    }
}

@media (max-width: 480px) {
    .cod-form {
        padding: 20px 15px;
    }
    
    .cod-fixture,
    .cod-result,
    .cod-clan-item {
        padding: 15px;
    }
    
    .cod-table {
        font-size: 12px;
    }
    
    .cod-table th,
    .cod-table td {
        padding: 6px 8px;
    }
}
