<?php
/**
 * User roles and capabilities management
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class COD_Clan_League_User_Roles {
    
    /**
     * Initialize user roles
     */
    public function init() {
        add_action('init', array($this, 'add_capabilities_to_existing_roles'));
    }
    
    /**
     * Add custom roles
     */
    public function add_roles() {
        // League Administrator role
        add_role(
            'cod_league_admin',
            __('League Administrator', 'cod-clan-league'),
            array(
                'read' => true,
                'edit_posts' => true,
                'delete_posts' => true,
                'publish_posts' => true,
                'upload_files' => true,
                
                // League management capabilities
                'manage_cod_leagues' => true,
                'manage_cod_seasons' => true,
                'manage_cod_clans' => true,
                'manage_cod_matches' => true,
                'approve_cod_results' => true,
                'override_cod_results' => true,
                'manage_cod_teams' => true,
                'view_cod_admin_panel' => true,
                
                // Clan capabilities
                'create_cod_clan' => true,
                'edit_cod_clan' => true,
                'delete_cod_clan' => true,
                'edit_others_cod_clans' => true,
                'delete_others_cod_clans' => true,
                
                // Season capabilities
                'create_cod_season' => true,
                'edit_cod_season' => true,
                'delete_cod_season' => true,
                'edit_others_cod_seasons' => true,
                'delete_others_cod_seasons' => true,
                
                // Match capabilities
                'create_cod_match' => true,
                'edit_cod_match' => true,
                'delete_cod_match' => true,
                'edit_others_cod_matches' => true,
                'delete_others_cod_matches' => true,
                'report_cod_match_results' => true,
            )
        );
        
        // Clan Captain role
        add_role(
            'cod_clan_captain',
            __('Clan Captain', 'cod-clan-league'),
            array(
                'read' => true,
                'upload_files' => true,
                
                // Clan management capabilities
                'create_cod_clan' => true,
                'edit_own_cod_clan' => true,
                'manage_clan_members' => true,
                'invite_clan_members' => true,
                'remove_clan_members' => true,
                
                // Match capabilities
                'report_cod_match_results' => true,
                'view_clan_matches' => true,
                'edit_clan_match_results' => true,
                
                // Team management
                'register_team_for_season' => true,
                'manage_team_roster' => true,
            )
        );
        
        // Clan Member role
        add_role(
            'cod_clan_member',
            __('Clan Member', 'cod-clan-league'),
            array(
                'read' => true,
                
                // Basic clan capabilities
                'view_clan_info' => true,
                'view_clan_matches' => true,
                'join_clan' => true,
                'leave_clan' => true,
            )
        );
    }
    
    /**
     * Add capabilities to existing roles
     */
    public function add_capabilities_to_existing_roles() {
        // Add capabilities to Administrator role
        $admin_role = get_role('administrator');
        if ($admin_role) {
            $admin_capabilities = array(
                'manage_cod_leagues',
                'manage_cod_seasons',
                'manage_cod_clans',
                'manage_cod_matches',
                'approve_cod_results',
                'override_cod_results',
                'manage_cod_teams',
                'view_cod_admin_panel',
                'create_cod_clan',
                'edit_cod_clan',
                'delete_cod_clan',
                'edit_others_cod_clans',
                'delete_others_cod_clans',
                'create_cod_season',
                'edit_cod_season',
                'delete_cod_season',
                'edit_others_cod_seasons',
                'delete_others_cod_seasons',
                'create_cod_match',
                'edit_cod_match',
                'delete_cod_match',
                'edit_others_cod_matches',
                'delete_others_cod_matches',
                'report_cod_match_results',
            );
            
            foreach ($admin_capabilities as $cap) {
                $admin_role->add_cap($cap);
            }
        }
        
        // Add basic capabilities to Editor role
        $editor_role = get_role('editor');
        if ($editor_role) {
            $editor_capabilities = array(
                'view_cod_admin_panel',
                'manage_cod_matches',
                'approve_cod_results',
                'edit_cod_clan',
                'edit_cod_season',
                'edit_cod_match',
                'report_cod_match_results',
            );
            
            foreach ($editor_capabilities as $cap) {
                $editor_role->add_cap($cap);
            }
        }
        
        // Add basic capabilities to Subscriber role
        $subscriber_role = get_role('subscriber');
        if ($subscriber_role) {
            $subscriber_capabilities = array(
                'view_clan_info',
                'view_clan_matches',
                'join_clan',
                'leave_clan',
            );
            
            foreach ($subscriber_capabilities as $cap) {
                $subscriber_role->add_cap($cap);
            }
        }
    }
    
    /**
     * Remove custom roles
     */
    public function remove_roles() {
        remove_role('cod_league_admin');
        remove_role('cod_clan_captain');
        remove_role('cod_clan_member');
        
        // Remove capabilities from existing roles
        $this->remove_capabilities_from_existing_roles();
    }
    
    /**
     * Remove capabilities from existing roles
     */
    private function remove_capabilities_from_existing_roles() {
        $capabilities_to_remove = array(
            'manage_cod_leagues',
            'manage_cod_seasons',
            'manage_cod_clans',
            'manage_cod_matches',
            'approve_cod_results',
            'override_cod_results',
            'manage_cod_teams',
            'view_cod_admin_panel',
            'create_cod_clan',
            'edit_cod_clan',
            'delete_cod_clan',
            'edit_others_cod_clans',
            'delete_others_cod_clans',
            'edit_own_cod_clan',
            'manage_clan_members',
            'invite_clan_members',
            'remove_clan_members',
            'create_cod_season',
            'edit_cod_season',
            'delete_cod_season',
            'edit_others_cod_seasons',
            'delete_others_cod_seasons',
            'create_cod_match',
            'edit_cod_match',
            'delete_cod_match',
            'edit_others_cod_matches',
            'delete_others_cod_matches',
            'report_cod_match_results',
            'view_clan_matches',
            'edit_clan_match_results',
            'register_team_for_season',
            'manage_team_roster',
            'view_clan_info',
            'join_clan',
            'leave_clan',
        );
        
        $roles = array('administrator', 'editor', 'author', 'contributor', 'subscriber');
        
        foreach ($roles as $role_name) {
            $role = get_role($role_name);
            if ($role) {
                foreach ($capabilities_to_remove as $cap) {
                    $role->remove_cap($cap);
                }
            }
        }
    }
    
    /**
     * Check if user has clan management permissions
     */
    public function user_can_manage_clan($user_id, $clan_id = null) {
        $user = get_user_by('id', $user_id);
        
        if (!$user) {
            return false;
        }
        
        // League admins can manage any clan
        if (user_can($user, 'manage_cod_clans')) {
            return true;
        }
        
        // Clan captains can manage their own clan
        if ($clan_id && user_can($user, 'edit_own_cod_clan')) {
            return $this->is_user_clan_captain($user_id, $clan_id);
        }
        
        return false;
    }
    
    /**
     * Check if user is captain of a specific clan
     */
    public function is_user_clan_captain($user_id, $clan_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'cod_clan_members';
        
        $result = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name 
             WHERE clan_id = %d AND user_id = %d AND role = 'captain' AND status = 'active'",
            $clan_id,
            $user_id
        ));
        
        return $result > 0;
    }
    
    /**
     * Check if user can report match results
     */
    public function user_can_report_match($user_id, $match_id = null) {
        $user = get_user_by('id', $user_id);
        
        if (!$user) {
            return false;
        }
        
        // League admins can report any match
        if (user_can($user, 'manage_cod_matches')) {
            return true;
        }
        
        // Clan captains can report matches for their teams
        if ($match_id && user_can($user, 'report_cod_match_results')) {
            return $this->is_user_involved_in_match($user_id, $match_id);
        }
        
        return false;
    }
    
    /**
     * Check if user is involved in a match (as captain of either team)
     */
    public function is_user_involved_in_match($user_id, $match_id) {
        global $wpdb;
        
        $matches_table = $wpdb->prefix . 'cod_matches';
        $members_table = $wpdb->prefix . 'cod_clan_members';
        $season_teams_table = $wpdb->prefix . 'cod_season_teams';
        
        $result = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $matches_table m
             INNER JOIN $season_teams_table st1 ON m.home_team_id = st1.id
             INNER JOIN $season_teams_table st2 ON m.away_team_id = st2.id
             INNER JOIN $members_table cm1 ON (st1.clan_id = cm1.clan_id AND cm1.user_id = %d AND cm1.role = 'captain' AND cm1.status = 'active')
             OR (st2.clan_id = cm1.clan_id AND cm1.user_id = %d AND cm1.role = 'captain' AND cm1.status = 'active')
             WHERE m.id = %d",
            $user_id,
            $user_id,
            $match_id
        ));
        
        return $result > 0;
    }
    
    /**
     * Get user's clan role
     */
    public function get_user_clan_role($user_id, $clan_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'cod_clan_members';
        
        return $wpdb->get_var($wpdb->prepare(
            "SELECT role FROM $table_name 
             WHERE clan_id = %d AND user_id = %d AND status = 'active'",
            $clan_id,
            $user_id
        ));
    }
    
    /**
     * Get user's clans
     */
    public function get_user_clans($user_id) {
        global $wpdb;
        
        $members_table = $wpdb->prefix . 'cod_clan_members';
        $clans_table = $wpdb->prefix . 'cod_clans';
        
        return $wpdb->get_results($wpdb->prepare(
            "SELECT c.*, cm.role, cm.status as member_status 
             FROM $clans_table c
             INNER JOIN $members_table cm ON c.id = cm.clan_id
             WHERE cm.user_id = %d AND cm.status = 'active'
             ORDER BY c.name",
            $user_id
        ));
    }
}
