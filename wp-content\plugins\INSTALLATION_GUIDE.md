# WordPress Plugins Installation & Testing Guide

This guide will help you install and test both the **COD Clan League** and **Site Chat System** plugins.

## 📋 Prerequisites

- WordPress 5.0 or higher
- PHP 7.4 or higher
- MySQL 5.6 or higher
- Admin access to WordPress

## 🚀 Installation Steps

### Step 1: Upload Plugin Files

1. **Upload the plugin folders** to your WordPress installation:
   ```
   wp-content/plugins/cod-clan-league/
   wp-content/plugins/site-chat-system/
   ```

2. **Set proper file permissions** (if needed):
   ```bash
   chmod -R 755 wp-content/plugins/cod-clan-league/
   chmod -R 755 wp-content/plugins/site-chat-system/
   ```

### Step 2: Activate Plugins

1. **Log in to WordPress Admin**
2. **Go to Plugins > Installed Plugins**
3. **Activate both plugins:**
   - ✅ COD Clan League
   - ✅ Site Chat System

### Step 3: Verify Installation

After activation, you should see new menu items in your WordPress admin:

**COD League Menu:**
- COD League > Dashboard
- COD League > Seasons
- COD League > Teams
- COD League > Matches
- COD League > Settings

**Site Chat Menu:**
- Site Chat > Dashboard
- Site Chat > Messages
- Site Chat > Settings

## 🎮 Testing COD Clan League Plugin

### 1. Configure Settings

1. **Go to COD League > Settings**
2. **Configure basic settings:**
   - Points for Win: 3
   - Points for Loss: 0
   - ✅ Require Screenshots
   - Maximum Clan Size: 10
   - Minimum Clan Size: 3
3. **Click "Save Settings"**

### 2. Create a Season

1. **Go to COD League > Seasons**
2. **Fill out the "Create New Season" form:**
   - Season Name: "Season 1"
   - Description: "First test season"
   - Start Date: Today's date
   - End Date: 30 days from now
   - Maximum Teams: 8
3. **Click "Create Season"**

### 3. Test Frontend Shortcodes

**Add these shortcodes to a page or post:**

```
[cod_league_table season_id="1"]
[cod_fixtures season_id="1"]
[cod_clan_list]
[cod_season_info season_id="1"]
```

### 4. Test Admin Functions

1. **Dashboard:** Check statistics display
2. **Seasons:** Try generating fixtures (after teams are registered)
3. **Teams:** View team registrations and approvals
4. **Matches:** Monitor match results and approvals

## 💬 Testing Site Chat System

### 1. Configure Chat Settings

1. **Go to Site Chat > Settings**
2. **Configure basic settings:**
   - ✅ Enable Chat
   - Max Message Length: 500
   - Message History Limit: 50
   - Refresh Interval: 3000ms
   - Chat Position: Bottom Right
   - Chat Theme: Default
3. **Click "Save Changes"**

### 2. Test Chat Widget

1. **Log in as a user** (chat only works for logged-in users)
2. **Visit any frontend page**
3. **Look for the chat icon** in the bottom right corner
4. **Click the chat icon** to open the chat window
5. **Send a test message**

### 3. Test with Multiple Users

1. **Open multiple browser windows/tabs**
2. **Log in as different users** in each
3. **Send messages between users**
4. **Test reactions, emojis, and online status**

## 🔧 Troubleshooting

### Common Issues

**1. Plugins not appearing after activation:**
- Check file permissions
- Verify all files were uploaded correctly
- Check WordPress error logs

**2. Database errors:**
- Ensure MySQL user has CREATE TABLE permissions
- Check WordPress database connection

**3. Chat not appearing:**
- Verify user is logged in
- Check browser console for JavaScript errors
- Ensure AJAX URL is correct

**4. Admin pages showing errors:**
- Check PHP error logs
- Verify all plugin files are present
- Ensure WordPress version compatibility

### Debug Mode

**Enable WordPress debug mode** for detailed error information:

```php
// Add to wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

### Database Tables

**COD Clan League creates these tables:**
- `wp_cod_clans`
- `wp_cod_clan_members`
- `wp_cod_seasons`
- `wp_cod_season_teams`
- `wp_cod_matches`
- `wp_cod_match_events`
- `wp_cod_clan_invitations`

**Site Chat System creates these tables:**
- `wp_chat_rooms`
- `wp_chat_messages`
- `wp_chat_room_participants`
- `wp_chat_online_users`
- `wp_chat_message_reactions`
- `wp_chat_blocked_users`

## 📊 Testing Checklist

### COD Clan League
- [ ] Plugin activates without errors
- [ ] Admin menu appears
- [ ] Settings page works
- [ ] Season creation works
- [ ] Shortcodes display correctly
- [ ] Database tables created
- [ ] Frontend styling loads

### Site Chat System
- [ ] Plugin activates without errors
- [ ] Admin menu appears
- [ ] Settings page works
- [ ] Chat widget appears for logged-in users
- [ ] Messages send and receive
- [ ] Online status updates
- [ ] Database tables created
- [ ] AJAX requests work

## 🎯 Next Steps

After successful installation and testing:

1. **Create user accounts** for testing clan functionality
2. **Set up clans** and register teams for seasons
3. **Configure chat permissions** and moderation settings
4. **Customize styling** to match your site design
5. **Set up regular backups** of your database
6. **Monitor performance** with multiple users

## 📞 Support

If you encounter issues:

1. **Check the troubleshooting section** above
2. **Review WordPress and PHP error logs**
3. **Verify all requirements are met**
4. **Test with default WordPress theme** to rule out theme conflicts
5. **Disable other plugins** to check for conflicts

## 🔄 Updates

To update the plugins:

1. **Backup your database** first
2. **Replace plugin files** with new versions
3. **Reactivate plugins** if needed
4. **Test functionality** after updates

---

**Happy Gaming! 🎮**
