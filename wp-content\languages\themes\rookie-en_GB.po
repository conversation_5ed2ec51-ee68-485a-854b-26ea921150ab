# Translation of Themes - Rookie in English (UK)
# This file is distributed under the same license as the Themes - Rookie package.
msgid ""
msgstr ""
"PO-Revision-Date: 2019-03-08 18:18:55+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/3.0.0-alpha.2\n"
"Language: en_GB\n"
"Project-Id-Version: Themes - Rookie\n"

#. Description of the theme
msgid "Rookie is a fully responsive theme made for sports organisations looking to use the SportsPress plugin. Once you’ve installed the theme and SportsPress, you'll be able to select a preset for your sport and demo content to help you get started on building your sports website."
msgstr "Rookie is a fully responsive theme made for sports organisations looking to use the SportsPress plugin. Once you’ve installed the theme and SportsPress, you'll be able to select a preset for your sport and demo content to help you get started on building your sports website."

#. Theme Name of the theme
#: inc/admin.php:147
msgid "Rookie"
msgstr "Rookie"

#: inc/admin.php:221
msgid "Rate this theme"
msgstr "Rate this theme"

#: inc/admin.php:221
msgid "%1$1s is proudly brought to you by %2$2s. If you like %3$3s: %4$4s."
msgstr "%1$1s is proudly brought to you by %2$2s. If you like %3$3s: %4$4s."

#: inc/admin.php:204
msgid "Access to private support"
msgstr "Access to private support"

#: inc/admin.php:203
msgid "Support forum"
msgstr "Support forum"

#: inc/admin.php:202
msgid "Tech support"
msgstr "Tech support"

#: inc/admin.php:197
msgid "Customizable footer copyright"
msgstr "Customisable footer copyright"

#: inc/admin.php:192
msgid "Multi-column news widget"
msgstr "Multi-column news widget"

#: inc/admin.php:187
msgid "Animated image slider"
msgstr "Animated image slider"

#: inc/admin.php:182
msgid "Social sidebar buttons"
msgstr "Social sidebar buttons"

#: inc/admin.php:177
msgid "Homepage template"
msgstr "Homepage template"

#: inc/admin.php:172
msgid "Second sidebar"
msgstr "Second sidebar"

#: inc/admin.php:167
msgid "Extended layout options"
msgstr "Extended layout options"

#: inc/admin.php:162
msgid "Responsive layout"
msgstr "Responsive layout"

#: inc/admin.php:157
msgid "View pricing"
msgstr "View pricing"

#: inc/admin.php:154
msgid "Free"
msgstr "Free"

#: inc/admin.php:153
msgid "Theme price"
msgstr "Theme price"

#: inc/admin.php:146
msgid "Features"
msgstr "Features"

#: inc/admin.php:141
msgid "Upgrade to Rookie Plus for more awesome features:"
msgstr "Upgrade to Rookie Plus for more awesome features:"

#: inc/admin.php:128
msgid "Theme Demos"
msgstr "Theme Demos"

#: inc/admin.php:124
msgid "The premium version of Rookie includes lots of additional features and options to customize your website. We have created several theme demos as examples in order to show what is possible with this flexible magazine theme."
msgstr "The premium version of Rookie includes lots of additional features and options to customise your website. We have created several theme demos as examples in order to show what is possible with this flexible magazine theme."

#: inc/admin.php:121
msgid "Rookie Theme Demos"
msgstr "Rookie Theme Demos"

#: inc/admin.php:114 inc/admin.php:211
msgid "Upgrade to Rookie Plus"
msgstr "Upgrade to Rookie Plus"

#: inc/admin.php:110
msgid "If you like the free version of this theme, you will LOVE the pro version of Rookie which includes unique custom widgets, additional features and more useful options to customize your website."
msgstr "If you like the free version of this theme, you will LOVE the pro version of Rookie which includes unique custom widgets, additional features and more useful options to customise your website."

#: inc/admin.php:107 inc/admin.php:148
msgid "Rookie Plus"
msgstr "Rookie Plus"

#: inc/admin.php:98
msgid "Customize Theme"
msgstr "Customise Theme"

#: inc/admin.php:94
msgid "%s supports the Theme Customizer for all theme settings. Click \"Customize Theme\" to open the Customizer now."
msgstr "%s supports the Theme Customiser for all theme settings. Click \"Customise Theme\" to open the Customiser now."

#: inc/admin.php:91
msgid "Theme Options"
msgstr "Theme Options"

#: inc/admin.php:77
msgid "Need any help with configuring %s? The documentation for this theme includes all theme related information that is needed to get your site up and running in no time. In case you have any additional questions, feel free to reach out in the theme support forums on WordPress.org."
msgstr "Need any help with configuring %s? The documentation for this theme includes all theme related information that is needed to get your site up and running in no time. In case you have any additional questions, feel free to reach out in the theme support forums on WordPress.org."

#: inc/admin.php:74 inc/admin.php:81
msgid "Theme Documentation"
msgstr "Theme Documentation"

#: inc/admin.php:67
msgid "Get Started with %s"
msgstr "Get Started with %s"

#: inc/admin.php:60 inc/admin.php:131
msgid "ThemeBoy Showcase"
msgstr "ThemeBoy Showcase"

#: inc/admin.php:57 inc/admin.php:84
msgid "Support Forum"
msgstr "Support Forum"

#: inc/admin.php:54
msgid "Support Center"
msgstr "Support Centre"

#: inc/admin.php:51
msgid "Theme Info Page"
msgstr "Theme Info Page"

#: inc/admin.php:49
msgid "Important Links:"
msgstr "Important Links:"

#: inc/admin.php:40
msgid "Theme Screenshot"
msgstr "Theme Screenshot"

#: inc/admin.php:36
msgid "Welcome to %1$1s %2$2s"
msgstr "Welcome to %1$1s %2$2s"

#: inc/admin.php:26
msgid "Theme Info"
msgstr "Theme Info"

#: inc/admin.php:26
msgid "Welcome to Rookie"
msgstr "Welcome to Rookie"

#: functions.php:1074
msgid "Roster"
msgstr "Roster"

#: functions.php:1059
msgid "Fixtures & Results"
msgstr "Fixtures & Results"

#: functions.php:197
msgid "Player Gallery"
msgstr "Player Gallery"

#: functions.php:190 functions.php:1068
msgid "League Table"
msgstr "League Table"

#: functions.php:183 functions.php:1062
msgid "Results"
msgstr "Results"

#: functions.php:175 functions.php:1061
msgid "Fixtures"
msgstr "Fixtures"

#: functions.php:162
msgid "Player List"
msgstr "Player List"

#: functions.php:155
msgid "Countdown"
msgstr "Countdown"

#: inc/customizer.php:298
msgid "Default"
msgstr "Default"

#: inc/customizer.php:302
msgid "None"
msgstr "None"

#: inc/customizer.php:301
msgid "Both"
msgstr "Both"

#: inc/customizer.php:298 inc/customizer.php:300
msgid "Right"
msgstr "Right"

#: inc/customizer.php:298 inc/customizer.php:299
msgid "Left"
msgstr "Left"

#: inc/customizer.php:268
msgid "Content Width"
msgstr "Content Width"

#: inc/customizer.php:254
msgid "Layout"
msgstr "Layout"

#. translators: %2$s: plugin name in screen reader markup
#: inc/class-tgm-plugin-activation.php:2614
msgid "Activate %2$s"
msgstr "Activate %2$s"

#. translators: %2$s: plugin name in screen reader markup
#: inc/class-tgm-plugin-activation.php:2608
msgid "Update %2$s"
msgstr "Update %2$s"

#. translators: %2$s: plugin name in screen reader markup
#: inc/class-tgm-plugin-activation.php:2603
msgid "Install %2$s"
msgstr "Install %2$s"

#: inc/class-tgm-plugin-activation.php:2533
msgid "No plugins to install, update or activate."
msgstr "No plugins to install, update, or activate."

#. translators: 1: install status, 2: update status
#: inc/class-tgm-plugin-activation.php:2342
msgctxt "Install/Update Status"
msgid "%1$s, %2$s"
msgstr "%1$s, %2$s"

#. translators: %s: version number
#: inc/class-tgm-plugin-activation.php:1982
msgid "TGMPA v%s"
msgstr "TGMPA v%s"

#: inc/class-tgm-plugin-activation.php:396
msgid "There are one or more required or recommended plugins to install, update or activate."
msgstr "There are one or more required or recommended plugins to install, update, or activate."

#. translators: %s: plugin name.
#: inc/class-tgm-plugin-activation.php:332
msgid "Updating Plugin: %s"
msgstr "Updating Plugin: %s"

#: functions.php:276 functions.php:286
msgid "Sidebar %d"
msgstr "Sidebar %d"

#: inc/customizer.php:199
msgid "Style"
msgstr "Style"

#: inc/customizer.php:187 inc/template-tags.php:100 functions.php:1027
msgid "Image"
msgstr "Image"

#: inc/customizer.php:186 inc/template-tags.php:99 functions.php:1026
msgid "Background"
msgstr "Background"

#: inc/customizer.php:243
msgid "Display post author?"
msgstr "Display post author?"

#: inc/customizer.php:225
msgid "Display post date?"
msgstr "Display post date?"

#: inc/customizer.php:211
msgid "Posts"
msgstr "Posts"

#: inc/customizer.php:95
msgid "Customize"
msgstr "Customise"

#: inc/customizer.php:44
msgid "Display Search Form"
msgstr "Display Search Form"

#: inc/customizer.php:62
msgid "Content Text Color"
msgstr "Content Text Colour"

#: functions.php:237
msgctxt "submit button"
msgid "Search"
msgstr "Search"

#. translators: 1: plugin name, 2: action number 3: total number of actions.
#: inc/class-tgm-plugin-activation.php:3498
msgid "Updating Plugin %1$s (%2$d/%3$d)"
msgstr "Updating Plugin %1$s (%2$d/%3$d)"

#: inc/class-tgm-plugin-activation.php:2934
msgid "No plugins are available to be activated at this time."
msgstr "No plugins are available to be activated at this time."

#: inc/class-tgm-plugin-activation.php:2908
msgid "No plugins were selected to be activated. No action taken."
msgstr "No plugins were selected to be activated. No action taken."

#: inc/class-tgm-plugin-activation.php:2802
msgid "No plugins are available to be updated at this time."
msgstr "No plugins are available to be updated at this time."

#: inc/class-tgm-plugin-activation.php:2800
msgid "No plugins are available to be installed at this time."
msgstr "No plugins are available to be installed at this time."

#: inc/class-tgm-plugin-activation.php:2759
msgid "No plugins were selected to be updated. No action taken."
msgstr "No plugins were selected to be updated. No action taken."

#: inc/class-tgm-plugin-activation.php:2757
msgid "No plugins were selected to be installed. No action taken."
msgstr "No plugins were selected to be installed. No action taken."

#: inc/class-tgm-plugin-activation.php:2723
msgid "Update"
msgstr "Update"

#: inc/class-tgm-plugin-activation.php:2684
msgid "Upgrade message from the plugin author:"
msgstr "Upgrade message from the plugin author:"

#: inc/class-tgm-plugin-activation.php:2553
msgid "Version"
msgstr "Version"

#: inc/class-tgm-plugin-activation.php:2510
msgid "Available version:"
msgstr "Available version:"

#: inc/class-tgm-plugin-activation.php:2498
msgid "Minimum required version:"
msgstr "Minimum required version:"

#: inc/class-tgm-plugin-activation.php:2490
msgid "Installed version:"
msgstr "Installed version:"

#: inc/class-tgm-plugin-activation.php:2482
msgctxt "as in: \"version nr unknown\""
msgid "unknown"
msgstr "unknown"

#. translators: 1: number of plugins.
#: inc/class-tgm-plugin-activation.php:2400
msgid "To Activate <span class=\"count\">(%s)</span>"
msgid_plural "To Activate <span class=\"count\">(%s)</span>"
msgstr[0] "To Activate <span class=\"count\">(%s)</span>"
msgstr[1] "To Activate <span class=\"count\">(%s)</span>"

#. translators: 1: number of plugins.
#: inc/class-tgm-plugin-activation.php:2396
msgid "Update Available <span class=\"count\">(%s)</span>"
msgid_plural "Update Available <span class=\"count\">(%s)</span>"
msgstr[0] "Update Available <span class=\"count\">(%s)</span>"
msgstr[1] "Update Available <span class=\"count\">(%s)</span>"

#. translators: 1: number of plugins.
#: inc/class-tgm-plugin-activation.php:2392
msgid "To Install <span class=\"count\">(%s)</span>"
msgid_plural "To Install <span class=\"count\">(%s)</span>"
msgstr[0] "To Install <span class=\"count\">(%s)</span>"
msgstr[1] "To Install <span class=\"count\">(%s)</span>"

#. translators: 1: number of plugins.
#: inc/class-tgm-plugin-activation.php:2388
msgctxt "plugins"
msgid "All <span class=\"count\">(%s)</span>"
msgid_plural "All <span class=\"count\">(%s)</span>"
msgstr[0] "All <span class=\"count\">(%s)</span>"
msgstr[1] "All <span class=\"count\">(%s)</span>"

#: inc/class-tgm-plugin-activation.php:2333
msgid "Update recommended"
msgstr "Update recommended"

#: inc/class-tgm-plugin-activation.php:2330
msgid "Requires Update"
msgstr "Requires Update"

#: inc/class-tgm-plugin-activation.php:2327
msgid "Required Update not Available"
msgstr "Required Update not Available"

#: inc/class-tgm-plugin-activation.php:2321
msgid "Active"
msgstr "Active"

#: inc/class-tgm-plugin-activation.php:2295
msgid "External Source"
msgstr "External Source"

#: inc/class-tgm-plugin-activation.php:1121
#: inc/class-tgm-plugin-activation.php:2948
msgctxt "plugin A *and* plugin B"
msgid "and"
msgstr "and"

#: inc/class-tgm-plugin-activation.php:937
msgid "The remote plugin package consists of more than one file, but the files are not packaged in a folder."
msgstr "The remote plugin package consists of more than one file, but the files are not packaged in a folder."

#: inc/class-tgm-plugin-activation.php:934
#: inc/class-tgm-plugin-activation.php:937
msgid "Please contact the plugin provider and ask them to package their plugin according to the WordPress guidelines."
msgstr "Please contact the plugin provider and ask them to package their plugin according to the WordPress guidelines."

#: inc/class-tgm-plugin-activation.php:934
msgid "The remote plugin package does not contain a folder with the desired slug and renaming did not work."
msgstr "The remote plugin package does not contain a folder with the desired slug and renaming did not work."

#: inc/class-tgm-plugin-activation.php:523
msgid "Update Required"
msgstr "Update Required"

#: inc/class-tgm-plugin-activation.php:522
msgid "This plugin needs to be updated to be compatible with your theme."
msgstr "This plugin needs to be updated to be compatible with your theme."

#: inc/class-tgm-plugin-activation.php:397
msgid "Please contact the administrator of this site for help."
msgstr "Please contact the administrator of this site for help."

#. translators: 1: plugin name.
#: inc/class-tgm-plugin-activation.php:392
msgid "Plugin not activated. A higher version of %s is needed for this theme. Please update the plugin."
msgstr "Plugin not activated. A higher version of %s is needed for this theme. Please update the plugin."

#. translators: 1: plugin name.
#: inc/class-tgm-plugin-activation.php:390
msgid "No action taken. Plugin %1$s was already active."
msgstr "No action taken. Plugin %1$s was already active."

#: inc/class-tgm-plugin-activation.php:375
msgid "Begin updating plugin"
msgid_plural "Begin updating plugins"
msgstr[0] "Begin updating plugin"
msgstr[1] "Begin updating plugins"

#. translators: 1: plugin name(s).
#: inc/class-tgm-plugin-activation.php:352
msgid "There is an update available for: %1$s."
msgid_plural "There are updates available for the following plugins: %1$s."
msgstr[0] "There is an update available for: %1$s."
msgstr[1] "There are updates available for the following plugins: %1$s."

#: inc/class-tgm-plugin-activation.php:333
msgid "Something went wrong with the plugin API."
msgstr "Something went wrong with the plugin API."

#. Template Name of the theme
msgid "Full Width"
msgstr "Full Width"

#: search.php:16
msgid "Search Results for: %s"
msgstr "Search Results for: %s"

#: inc/template-tags.php:345
msgid "Archives"
msgstr "Archives"

#. translators: 1: Taxonomy singular name, 2: Current taxonomy term
#: inc/template-tags.php:343
msgid "%1$s: %2$s"
msgstr "%1$s: %2$s"

#: inc/template-tags.php:339
msgid "Archives: %s"
msgstr "Archives: %s"

#: inc/template-tags.php:337
msgctxt "post format archive title"
msgid "Chats"
msgstr "Chats"

#: inc/template-tags.php:335
msgctxt "post format archive title"
msgid "Audio"
msgstr "Audio"

#: inc/template-tags.php:333
msgctxt "post format archive title"
msgid "Statuses"
msgstr "Statuses"

#: inc/template-tags.php:331
msgctxt "post format archive title"
msgid "Links"
msgstr "Links"

#: inc/template-tags.php:329
msgctxt "post format archive title"
msgid "Quotes"
msgstr "Quotes"

#: inc/template-tags.php:327
msgctxt "post format archive title"
msgid "Videos"
msgstr "Videos"

#: inc/template-tags.php:325
msgctxt "post format archive title"
msgid "Images"
msgstr "Images"

#: inc/template-tags.php:323
msgctxt "post format archive title"
msgid "Galleries"
msgstr "Galleries"

#: inc/template-tags.php:321
msgctxt "post format archive title"
msgid "Asides"
msgstr "Asides"

#: inc/template-tags.php:319
msgctxt "daily archives date format"
msgid "F j, Y"
msgstr "F j, Y"

#: inc/template-tags.php:319
msgid "Day: %s"
msgstr "Day: %s"

#: inc/template-tags.php:317
msgctxt "monthly archives date format"
msgid "F Y"
msgstr "F Y"

#: inc/template-tags.php:317
msgid "Month: %s"
msgstr "Month: %s"

#: inc/template-tags.php:315
msgctxt "yearly archives date format"
msgid "Y"
msgstr "Y"

#: inc/template-tags.php:315
msgid "Year: %s"
msgstr "Year: %s"

#: inc/template-tags.php:313
msgid "Author: %s"
msgstr "Author: %s"

#: inc/template-tags.php:311
msgid "Tag: %s"
msgstr "Tag: %s"

#: inc/template-tags.php:309
msgid "Category: %s"
msgstr "Category: %s"

#: inc/template-tags.php:241
msgctxt "post author"
msgid "by %s"
msgstr "By %s"

#: inc/template-tags.php:197
msgctxt "Next post link"
msgid "%title&nbsp;<span class=\"meta-nav\">&rarr;</span>"
msgstr "%title&nbsp;<span class=\"meta-nav\">&rarr;</span>"

#: inc/template-tags.php:196
msgctxt "Previous post link"
msgid "<span class=\"meta-nav\">&larr;</span>&nbsp;%title"
msgstr "<span class=\"meta-nav\">&larr;</span>&nbsp;%title"

#: inc/template-tags.php:193
msgid "Post navigation"
msgstr "Post navigation"

#: inc/template-tags.php:170
msgid "Newer posts <span class=\"meta-nav\">&rarr;</span>"
msgstr "Newer posts <span class=\"meta-nav\">&rarr;</span>"

#: inc/template-tags.php:166
msgid "<span class=\"meta-nav\">&larr;</span> Older posts"
msgstr "<span class=\"meta-nav\">&larr;</span> Older posts"

#: inc/template-tags.php:162
msgid "Posts navigation"
msgstr "Posts navigation"

#: inc/customizer.php:177
msgid "Widget Heading Color"
msgstr "Widget Heading Colour"

#: inc/customizer.php:161
msgid "Widget Background Color"
msgstr "Widget Background Colour"

#: inc/customizer.php:78
msgid "Content Background Color"
msgstr "Content Background Colour"

#: inc/customizer.php:145
msgid "Text Color"
msgstr "Text Colour"

#: inc/customizer.php:129
msgid "Link Color"
msgstr "Link Colour"

#: inc/customizer.php:113
msgid "Primary Color"
msgstr "Primary Colour"

#: inc/customizer.php:29
msgid "Logo"
msgstr "Logo"

#. translators: 1: plugin name, 2: action number 3: total number of actions.
#: inc/class-tgm-plugin-activation.php:3520
msgid "Installing Plugin %1$s (%2$d/%3$d)"
msgstr "Installing Plugin %1$s (%2$d/%3$d)"

#: inc/class-tgm-plugin-activation.php:3518
msgid "All installations have been completed."
msgstr "All installations have been completed."

#. translators: 1: plugin name.
#: inc/class-tgm-plugin-activation.php:3517
msgid "%1$s installed successfully."
msgstr "%1$s installed successfully."

#. translators: 1: plugin name.
#: inc/class-tgm-plugin-activation.php:3503
msgid "The installation of %1$s failed."
msgstr "The installation of %1$s failed."

#. translators: 1: plugin name, 2: error message.
#: inc/class-tgm-plugin-activation.php:3501
msgid "An error occurred while installing %1$s: <strong>%2$s</strong>."
msgstr "An error occurred while installing %1$s: <strong>%2$s</strong>."

#: inc/class-tgm-plugin-activation.php:3515
msgid "The installation process is starting. This process may take a while on some hosts, so please be patient."
msgstr "The installation process is starting. This process may take a while on some hosts, so please be patient."

#. translators: 1: plugin name, 2: action number 3: total number of actions.
#: inc/class-tgm-plugin-activation.php:3512
msgid "Installing and Activating Plugin %1$s (%2$d/%3$d)"
msgstr "Installing and Activating Plugin %1$s (%2$d/%3$d)"

#: inc/class-tgm-plugin-activation.php:3510
msgid "All installations and activations have been completed."
msgstr "All installations and activations have been completed."

#. translators: 1: plugin name.
#: inc/class-tgm-plugin-activation.php:3509
#: inc/class-tgm-plugin-activation.php:3517
msgid "Hide Details"
msgstr "Hide Details"

#. translators: 1: plugin name.
#: inc/class-tgm-plugin-activation.php:3509
#: inc/class-tgm-plugin-activation.php:3517
msgid "Show Details"
msgstr "Show Details"

#. translators: 1: plugin name.
#: inc/class-tgm-plugin-activation.php:3509
msgid "%1$s installed and activated successfully."
msgstr "%1$s installed and activated successfully."

#: inc/class-tgm-plugin-activation.php:3507
msgid "The installation and activation process is starting. This process may take a while on some hosts, so please be patient."
msgstr "The installation and activation process is starting. This process may take a while on some hosts, so please be patient."

#: inc/class-tgm-plugin-activation.php:3158
msgid "Plugin activation failed."
msgstr "Plugin activation failed."

#: inc/class-tgm-plugin-activation.php:2554
msgid "Status"
msgstr "Status"

#: inc/class-tgm-plugin-activation.php:2549
msgid "Type"
msgstr "Type"

#: inc/class-tgm-plugin-activation.php:2548
msgid "Source"
msgstr "Source"

#: inc/class-tgm-plugin-activation.php:2547
msgid "Plugin"
msgstr "Plugin"

#: inc/class-tgm-plugin-activation.php:2726
msgid "Activate"
msgstr "Activate"

#: inc/class-tgm-plugin-activation.php:2717
msgid "Install"
msgstr "Install"

#: inc/class-tgm-plugin-activation.php:2319
msgid "Installed But Not Activated"
msgstr "Installed But Not Activated"

#: inc/class-tgm-plugin-activation.php:2315
msgid "Not Installed"
msgstr "Not Installed"

#: inc/class-tgm-plugin-activation.php:2276
msgid "Recommended"
msgstr "RECOMMENDED"

#: inc/class-tgm-plugin-activation.php:2273
msgid "Required"
msgstr "REQUIRED"

#: inc/class-tgm-plugin-activation.php:2292
msgid "WordPress Repository"
msgstr "WordPress Repository"

#: inc/class-tgm-plugin-activation.php:2298
msgid "Pre-Packaged"
msgstr "Pre-Packaged"

#: inc/class-tgm-plugin-activation.php:386
#: inc/class-tgm-plugin-activation.php:827
#: inc/class-tgm-plugin-activation.php:2533
#: inc/class-tgm-plugin-activation.php:3580
msgid "Return to the Dashboard"
msgstr "Return to the Dashboard"

#: inc/class-tgm-plugin-activation.php:395
msgid "Dismiss this notice"
msgstr "Dismiss this notice"

#. translators: 1: dashboard link.
#: inc/class-tgm-plugin-activation.php:394
msgid "All plugins installed and activated successfully. %1$s"
msgstr "All plugins installed and activated successfully. %1$s"

#: inc/class-tgm-plugin-activation.php:388
#: inc/class-tgm-plugin-activation.php:2952
msgid "The following plugin was activated successfully:"
msgid_plural "The following plugins were activated successfully:"
msgstr[0] "The following plugin was activated successfully:"
msgstr[1] ""

#: inc/class-tgm-plugin-activation.php:387
#: inc/class-tgm-plugin-activation.php:3159
msgid "Plugin activated successfully."
msgstr "Plugin activated successfully."

#: inc/class-tgm-plugin-activation.php:385
msgid "Return to Required Plugins Installer"
msgstr "Return to Required Plugins Installer"

#: inc/class-tgm-plugin-activation.php:380
msgid "Begin activating plugin"
msgid_plural "Begin activating plugins"
msgstr[0] "Begin activating plugin"
msgstr[1] "Begin activating plugins"

#: inc/class-tgm-plugin-activation.php:370
msgid "Begin installing plugin"
msgid_plural "Begin installing plugins"
msgstr[0] "Begin installing plugin"
msgstr[1] "Begin installing plugins"

#. translators: 1: plugin name(s).
#: inc/class-tgm-plugin-activation.php:346
msgid "The following plugin needs to be updated to its latest version to ensure maximum compatibility with this theme: %1$s."
msgid_plural "The following plugins need to be updated to their latest version to ensure maximum compatibility with this theme: %1$s."
msgstr[0] "The following plugin needs to be updated to its latest version to ensure maximum compatibility with this theme: %1$s."
msgstr[1] "The following plugins need to be updated to their latest version to ensure maximum compatibility with this theme: %1$s."

#. translators: 1: plugin name(s).
#: inc/class-tgm-plugin-activation.php:364
msgid "The following recommended plugin is currently inactive: %1$s."
msgid_plural "The following recommended plugins are currently inactive: %1$s."
msgstr[0] "The following recommended plugin is currently inactive: %1$s."
msgstr[1] "The following recommended plugins are currently inactive: %1$s."

#. translators: 1: plugin name(s).
#: inc/class-tgm-plugin-activation.php:358
msgid "The following required plugin is currently inactive: %1$s."
msgid_plural "The following required plugins are currently inactive: %1$s."
msgstr[0] "The following required plugin is currently inactive: %1$s."
msgstr[1] "The following required plugins are currently inactive: %1$s."

#. translators: 1: plugin name(s).
#: inc/class-tgm-plugin-activation.php:340
msgid "This theme recommends the following plugin: %1$s."
msgid_plural "This theme recommends the following plugins: %1$s."
msgstr[0] "This theme recommends the following plugin: %1$s."
msgstr[1] "This theme recommends the following plugins: %1$s."

#. translators: 1: plugin name(s).
#: inc/class-tgm-plugin-activation.php:334
msgid "This theme requires the following plugin: %1$s."
msgid_plural "This theme requires the following plugins: %1$s."
msgstr[0] "This theme requires the following plugin: %1$s."
msgstr[1] "This theme requires the following plugins: %1$s."

#. translators: %s: plugin name.
#: inc/class-tgm-plugin-activation.php:330
msgid "Installing Plugin: %s"
msgstr "Installing Plugin: %s"

#: inc/class-tgm-plugin-activation.php:328
msgid "Install Plugins"
msgstr "Install Plugins"

#: inc/class-tgm-plugin-activation.php:327
msgid "Install Required Plugins"
msgstr "Install Required Plugins"

#: header.php:23
msgid "Skip to content"
msgstr "Skip to content"

#: functions.php:916
msgid "Designed by %s"
msgstr "Designed by %s"

#: functions.php:903
msgctxt "copyright info"
msgid "&copy; %1$s %2$s"
msgstr "&copy; %1$s %2$s"

#: functions.php:320
msgid "Widgetized Footer Region %d."
msgstr "Widgetised Footer Region %d."

#: functions.php:318
msgid "Footer %d"
msgstr "Footer %d"

#. Template Name of the theme
#: functions.php:307
msgid "Homepage"
msgstr "Homepage"

#: functions.php:297
msgid "Header"
msgstr "Header"

#: inc/customizer.php:293 functions.php:266
msgid "Sidebar"
msgstr "Sidebar"

#: functions.php:83 functions.php:126
msgid "Primary Menu"
msgstr "Primary Menu"

#: content.php:31
msgid "Continue reading %s <span class=\"meta-nav\">&rarr;</span>"
msgstr "Continue reading %s <span class=\"meta-nav\">&rarr;</span>"

#: content-single.php:34 content.php:38 content-notitle.php:26
#: content-nothumb.php:18 content-page.php:24
msgid "Pages:"
msgstr "Pages:"

#: content-none.php:28
msgid "It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help."
msgstr "It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help."

#: content-none.php:23
msgid "Sorry, but nothing matched your search terms. Please try again with some different keywords."
msgstr "Sorry, but nothing matched your search terms. Please try again with some different keywords."

#: content-none.php:19
msgid "Ready to publish your first post? <a href=\"%1$s\">Get started here</a>."
msgstr "Ready to publish your first post? <a href=\"%1$s\">Get started here</a>."

#: content-none.php:13
msgid "Nothing Found"
msgstr "Nothing Found"

#: comments.php:73
msgid "Join the Conversation"
msgstr "Join the Conversation"

#: comments.php:71
msgid "Start a Conversation"
msgstr "Start a Conversation"

#: comments.php:65
msgid "Comments are closed."
msgstr "Comments are closed."

#: comments.php:37 comments.php:55
msgid "Newer Comments &rarr;"
msgstr "Newer Comments &rarr;"

#: comments.php:36 comments.php:54
msgid "&larr; Older Comments"
msgstr "&larr; Older Comments"

#: comments.php:35 comments.php:53
msgid "Comment navigation"
msgstr "Comment navigation"

#: comments.php:28
msgctxt "comments title"
msgid "One comment"
msgid_plural "%1$s comments"
msgstr[0] "One comment"
msgstr[1] "%1$s comments"

#: 404.php:19
msgid "It looks like nothing was found at this location. Maybe try one of the links below or a search?"
msgstr "It looks like nothing was found at this location. Maybe try one of the links below or a search?"

#: 404.php:15
msgid "Oops! That page can&rsquo;t be found."
msgstr "Oops! That page can&rsquo;t be found."

#. Author URI of the theme
msgid "https://www.themeboy.com"
msgstr "https://www.themeboy.com"

#. Author of the theme
msgid "ThemeBoy"
msgstr "ThemeBoy"

#. Theme URI of the theme
msgid "https://www.themeboy.com/rookie/"
msgstr "https://www.themeboy.com/rookie/"