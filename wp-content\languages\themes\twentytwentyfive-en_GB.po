# Translation of Themes - Twenty Twenty-Five in English (UK)
# This file is distributed under the same license as the Themes - Twenty Twenty-Five package.
msgid ""
msgstr ""
"PO-Revision-Date: 2023-12-04 20:56:03+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: en_GB\n"
"Project-Id-Version: Themes - Twenty Twenty-Five\n"

#: theme.json
msgctxt "Template part name"
msgid "Header"
msgstr "Header"

#: theme.json
msgctxt "Template part name"
msgid "Footer"
msgstr "Footer"

#: theme.json styles/02-noon.json styles/04-afternoon.json
#: styles/05-twilight.json styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json
msgctxt "Font size name"
msgid "Extra Large"
msgstr "Extra Large"

#: theme.json styles/02-noon.json styles/04-afternoon.json
#: styles/05-twilight.json styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json
msgctxt "Font size name"
msgid "Large"
msgstr "Large"

#: theme.json styles/02-noon.json styles/04-afternoon.json
#: styles/05-twilight.json styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json
msgctxt "Font size name"
msgid "Medium"
msgstr "Medium"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Contrast"
msgstr "Contrast"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Base"
msgstr "Base"

#: functions.php:76
msgid "Checkmark"
msgstr "Checkmark"

#: patterns/contact-info-locations.php:33 patterns/footer-social.php:22
#: patterns/media-instagram-grid.php:24 patterns/page-cv-bio.php:47
#: patterns/page-link-in-bio-heading-paragraph-links-image.php:36
#: patterns/page-link-in-bio-with-tight-margins.php:48
msgid "Instagram"
msgstr "Instagram"

#: patterns/contact-info-locations.php:34 patterns/footer-social.php:21
msgid "Facebook"
msgstr "Facebook"

#: patterns/footer-columns.php:37 patterns/footer.php:47
msgid "About"
msgstr "About"

#: patterns/hidden-404.php
msgctxt "Pattern title"
msgid "404"
msgstr "404"

#: patterns/comments.php
msgctxt "Pattern title"
msgid "Comments"
msgstr "Comments"

#: patterns/comments.php:18
msgid "Comments"
msgstr "Comments"

#: patterns/post-navigation.php
msgctxt "Pattern title"
msgid "Post navigation"
msgstr "Post navigation"

#: patterns/hidden-search.php
msgctxt "Pattern title"
msgid "Search"
msgstr "Search"

#: theme.json styles/02-noon.json styles/04-afternoon.json
#: styles/05-twilight.json styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json
msgctxt "Font size name"
msgid "Small"
msgstr "Small"

#: theme.json styles/02-noon.json styles/04-afternoon.json
#: styles/05-twilight.json styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json
msgctxt "Font size name"
msgid "Extra Extra Large"
msgstr "Extra Extra Large"

#: theme.json
msgctxt "Template part name"
msgid "Sidebar"
msgstr "Sidebar"

#: theme.json
msgctxt "Custom template name"
msgid "Page No Title"
msgstr "Page No Title"

#: functions.php:106
msgid "A collection of full page layouts."
msgstr "A collection of full-page layouts."

#: patterns/hidden-sidebar.php
msgctxt "Pattern title"
msgid "Sidebar"
msgstr "Sidebar"

#: patterns/template-query-loop.php
msgctxt "Pattern title"
msgid "List of posts, 1 column"
msgstr "List of posts, 1 column"

#. Author of the theme
#: style.css
#, gp-priority: low
msgid "the WordPress team"
msgstr "the WordPress team"

#. Author URI of the theme
#: style.css patterns/footer-centered.php:34 patterns/footer-columns.php:74
#: patterns/footer-newsletter.php:50 patterns/footer-social.php:36
#: patterns/footer.php:83
#, gp-priority: low
msgid "https://wordpress.org"
msgstr "https://en-gb.wordpress.org"
