<?php
/**
 * Chat widget template
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$options = get_option('site_chat_options', array());
$position = $options['chat_position'] ?? 'bottom-right';
$theme = $options['chat_theme'] ?? 'default';
$current_user = wp_get_current_user();
$chat_manager = new Site_Chat_Manager();
$default_room = $chat_manager->get_default_room();

if (!$default_room) {
    return; // No chat room available
}
?>

<div id="site-chat-widget" class="site-chat-widget site-chat-position-<?php echo esc_attr($position); ?> site-chat-theme-<?php echo esc_attr($theme); ?>" data-room-id="<?php echo $default_room->id; ?>">
    <!-- Chat Toggle Button -->
    <div class="site-chat-toggle" id="site-chat-toggle">
        <span class="site-chat-icon">💬</span>
        <span class="site-chat-notification-badge" id="site-chat-notification-badge" style="display: none;">0</span>
    </div>

    <!-- Chat Window -->
    <div class="site-chat-window" id="site-chat-window" style="display: none;">
        <!-- Chat Header -->
        <div class="site-chat-header">
            <div class="site-chat-header-info">
                <h4><?php echo esc_html($default_room->name); ?></h4>
                <span class="site-chat-online-count" id="site-chat-online-count">0 <?php _e('online', 'site-chat-system'); ?></span>
            </div>
            <div class="site-chat-header-actions">
                <button class="site-chat-minimize" id="site-chat-minimize" title="<?php _e('Minimize', 'site-chat-system'); ?>">−</button>
                <button class="site-chat-close" id="site-chat-close" title="<?php _e('Close', 'site-chat-system'); ?>">×</button>
            </div>
        </div>

        <!-- Chat Messages Area -->
        <div class="site-chat-messages" id="site-chat-messages">
            <div class="site-chat-loading" id="site-chat-loading">
                <span><?php _e('Loading messages...', 'site-chat-system'); ?></span>
            </div>
        </div>

        <!-- Online Users Sidebar -->
        <div class="site-chat-users-sidebar" id="site-chat-users-sidebar" style="display: none;">
            <div class="site-chat-users-header">
                <h5><?php _e('Online Users', 'site-chat-system'); ?></h5>
                <button class="site-chat-users-close" id="site-chat-users-close">×</button>
            </div>
            <div class="site-chat-users-list" id="site-chat-users-list">
                <!-- Online users will be populated here -->
            </div>
        </div>

        <!-- Chat Input Area -->
        <div class="site-chat-input-area">
            <div class="site-chat-input-wrapper">
                <textarea
                    id="site-chat-input"
                    class="site-chat-input"
                    placeholder="<?php _e('Type a message...', 'site-chat-system'); ?>"
                    maxlength="<?php echo intval($options['max_message_length'] ?? 500); ?>"
                    rows="1"
                ></textarea>
                <div class="site-chat-input-actions">
                    <button class="site-chat-emoji-btn" id="site-chat-emoji-btn" title="<?php _e('Emojis', 'site-chat-system'); ?>">😊</button>
                    <button class="site-chat-users-btn" id="site-chat-users-btn" title="<?php _e('Online Users', 'site-chat-system'); ?>">👥</button>
                    <button class="site-chat-send-btn" id="site-chat-send-btn" title="<?php _e('Send Message', 'site-chat-system'); ?>">
                        <span class="site-chat-send-icon">➤</span>
                    </button>
                </div>
            </div>
            <div class="site-chat-typing-indicator" id="site-chat-typing-indicator" style="display: none;">
                <span><?php _e('Someone is typing...', 'site-chat-system'); ?></span>
            </div>
        </div>

        <!-- Emoji Picker -->
        <div class="site-chat-emoji-picker" id="site-chat-emoji-picker" style="display: none;">
            <div class="site-chat-emoji-grid">
                <span class="site-chat-emoji" data-emoji="😊">😊</span>
                <span class="site-chat-emoji" data-emoji="😂">😂</span>
                <span class="site-chat-emoji" data-emoji="❤️">❤️</span>
                <span class="site-chat-emoji" data-emoji="👍">👍</span>
                <span class="site-chat-emoji" data-emoji="👎">👎</span>
                <span class="site-chat-emoji" data-emoji="😢">😢</span>
                <span class="site-chat-emoji" data-emoji="😮">😮</span>
                <span class="site-chat-emoji" data-emoji="😡">😡</span>
                <span class="site-chat-emoji" data-emoji="🎉">🎉</span>
                <span class="site-chat-emoji" data-emoji="🔥">🔥</span>
                <span class="site-chat-emoji" data-emoji="💯">💯</span>
                <span class="site-chat-emoji" data-emoji="⚡">⚡</span>
            </div>
        </div>
    </div>

    <!-- Connection Status -->
    <div class="site-chat-connection-status" id="site-chat-connection-status" style="display: none;">
        <span class="site-chat-status-text"><?php _e('Connecting...', 'site-chat-system'); ?></span>
    </div>
</div>

<!-- Chat Message Template -->
<script type="text/template" id="site-chat-message-template">
    <div class="site-chat-message {{messageClass}}" data-message-id="{{messageId}}" data-user-id="{{userId}}">
        <div class="site-chat-message-avatar">
            <img src="{{avatarUrl}}" alt="{{userName}}" />
        </div>
        <div class="site-chat-message-content">
            <div class="site-chat-message-header">
                <span class="site-chat-message-author">{{userName}}</span>
                <span class="site-chat-message-time">{{messageTime}}</span>
                {{#if isEdited}}
                <span class="site-chat-message-edited"><?php _e('(edited)', 'site-chat-system'); ?></span>
                {{/if}}
            </div>
            <div class="site-chat-message-text">{{messageText}}</div>
            {{#if reactions}}
            <div class="site-chat-message-reactions">
                {{#each reactions}}
                <span class="site-chat-reaction" data-reaction="{{reaction}}">
                    {{reaction}} <span class="site-chat-reaction-count">{{count}}</span>
                </span>
                {{/each}}
            </div>
            {{/if}}
            <div class="site-chat-message-actions" style="display: none;">
                <button class="site-chat-action-react" title="<?php _e('React', 'site-chat-system'); ?>">👍</button>
                {{#if canEdit}}
                <button class="site-chat-action-edit" title="<?php _e('Edit', 'site-chat-system'); ?>">✏️</button>
                {{/if}}
                {{#if canDelete}}
                <button class="site-chat-action-delete" title="<?php _e('Delete', 'site-chat-system'); ?>">🗑️</button>
                {{/if}}
            </div>
        </div>
    </div>
</script>

<!-- Online User Template -->
<script type="text/template" id="site-chat-user-template">
    <div class="site-chat-user" data-user-id="{{userId}}">
        <div class="site-chat-user-avatar">
            <img src="{{avatarUrl}}" alt="{{userName}}" />
            <span class="site-chat-user-status site-chat-status-{{status}}"></span>
        </div>
        <div class="site-chat-user-info">
            <span class="site-chat-user-name">{{userName}}</span>
            <span class="site-chat-user-status-text">{{statusText}}</span>
        </div>
    </div>
</script>

<script>
// Initialize chat widget when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (typeof SiteChatWidget !== 'undefined') {
        SiteChatWidget.init();
    }
});
</script>