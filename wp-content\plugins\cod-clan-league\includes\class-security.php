<?php
/**
 * Security and validation utilities
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class COD_Clan_League_Security {
    
    /**
     * Validate and sanitize clan data
     */
    public static function validate_clan_data($data) {
        $errors = array();
        $sanitized = array();
        
        // Name validation
        if (empty($data['name'])) {
            $errors[] = __('Clan name is required.', 'cod-clan-league');
        } elseif (strlen($data['name']) > 100) {
            $errors[] = __('Clan name must be 100 characters or less.', 'cod-clan-league');
        } else {
            $sanitized['name'] = sanitize_text_field($data['name']);
        }
        
        // Tag validation
        if (empty($data['tag'])) {
            $errors[] = __('Clan tag is required.', 'cod-clan-league');
        } elseif (strlen($data['tag']) > 10) {
            $errors[] = __('Clan tag must be 10 characters or less.', 'cod-clan-league');
        } elseif (!preg_match('/^[A-Za-z0-9_-]+$/', $data['tag'])) {
            $errors[] = __('Clan tag can only contain letters, numbers, hyphens, and underscores.', 'cod-clan-league');
        } else {
            $sanitized['tag'] = sanitize_text_field($data['tag']);
        }
        
        // Description validation
        if (!empty($data['description'])) {
            if (strlen($data['description']) > 1000) {
                $errors[] = __('Description must be 1000 characters or less.', 'cod-clan-league');
            } else {
                $sanitized['description'] = sanitize_textarea_field($data['description']);
            }
        }
        
        // Logo URL validation
        if (!empty($data['logo_url'])) {
            if (!filter_var($data['logo_url'], FILTER_VALIDATE_URL)) {
                $errors[] = __('Logo URL must be a valid URL.', 'cod-clan-league');
            } else {
                $sanitized['logo_url'] = esc_url_raw($data['logo_url']);
            }
        }
        
        // Captain ID validation
        if (empty($data['captain_id'])) {
            $errors[] = __('Captain is required.', 'cod-clan-league');
        } elseif (!get_user_by('id', $data['captain_id'])) {
            $errors[] = __('Invalid captain user ID.', 'cod-clan-league');
        } else {
            $sanitized['captain_id'] = intval($data['captain_id']);
        }
        
        return array(
            'errors' => $errors,
            'data' => $sanitized,
            'valid' => empty($errors)
        );
    }
    
    /**
     * Validate and sanitize season data
     */
    public static function validate_season_data($data) {
        $errors = array();
        $sanitized = array();
        
        // Name validation
        if (empty($data['name'])) {
            $errors[] = __('Season name is required.', 'cod-clan-league');
        } elseif (strlen($data['name']) > 100) {
            $errors[] = __('Season name must be 100 characters or less.', 'cod-clan-league');
        } else {
            $sanitized['name'] = sanitize_text_field($data['name']);
        }
        
        // Description validation
        if (!empty($data['description'])) {
            if (strlen($data['description']) > 1000) {
                $errors[] = __('Description must be 1000 characters or less.', 'cod-clan-league');
            } else {
                $sanitized['description'] = sanitize_textarea_field($data['description']);
            }
        }
        
        // Date validation
        if (empty($data['start_date'])) {
            $errors[] = __('Start date is required.', 'cod-clan-league');
        } elseif (!self::validate_date($data['start_date'])) {
            $errors[] = __('Invalid start date format.', 'cod-clan-league');
        } else {
            $sanitized['start_date'] = sanitize_text_field($data['start_date']);
        }
        
        if (empty($data['end_date'])) {
            $errors[] = __('End date is required.', 'cod-clan-league');
        } elseif (!self::validate_date($data['end_date'])) {
            $errors[] = __('Invalid end date format.', 'cod-clan-league');
        } else {
            $sanitized['end_date'] = sanitize_text_field($data['end_date']);
        }
        
        // Date logic validation
        if (!empty($sanitized['start_date']) && !empty($sanitized['end_date'])) {
            if (strtotime($sanitized['start_date']) >= strtotime($sanitized['end_date'])) {
                $errors[] = __('End date must be after start date.', 'cod-clan-league');
            }
        }
        
        // Max teams validation
        if (isset($data['max_teams'])) {
            $max_teams = intval($data['max_teams']);
            if ($max_teams < 2) {
                $errors[] = __('Maximum teams must be at least 2.', 'cod-clan-league');
            } elseif ($max_teams > 64) {
                $errors[] = __('Maximum teams cannot exceed 64.', 'cod-clan-league');
            } else {
                $sanitized['max_teams'] = $max_teams;
            }
        }
        
        return array(
            'errors' => $errors,
            'data' => $sanitized,
            'valid' => empty($errors)
        );
    }
    
    /**
     * Validate and sanitize match data
     */
    public static function validate_match_data($data) {
        $errors = array();
        $sanitized = array();
        
        // Score validation
        if (isset($data['home_score'])) {
            $home_score = intval($data['home_score']);
            if ($home_score < 0) {
                $errors[] = __('Home score cannot be negative.', 'cod-clan-league');
            } else {
                $sanitized['home_score'] = $home_score;
            }
        }
        
        if (isset($data['away_score'])) {
            $away_score = intval($data['away_score']);
            if ($away_score < 0) {
                $errors[] = __('Away score cannot be negative.', 'cod-clan-league');
            } else {
                $sanitized['away_score'] = $away_score;
            }
        }
        
        // Notes validation
        if (!empty($data['notes'])) {
            if (strlen($data['notes']) > 1000) {
                $errors[] = __('Notes must be 1000 characters or less.', 'cod-clan-league');
            } else {
                $sanitized['notes'] = sanitize_textarea_field($data['notes']);
            }
        }
        
        // Screenshot URL validation
        if (!empty($data['screenshot_url'])) {
            if (!filter_var($data['screenshot_url'], FILTER_VALIDATE_URL)) {
                $errors[] = __('Screenshot URL must be a valid URL.', 'cod-clan-league');
            } else {
                $sanitized['screenshot_url'] = esc_url_raw($data['screenshot_url']);
            }
        }
        
        return array(
            'errors' => $errors,
            'data' => $sanitized,
            'valid' => empty($errors)
        );
    }
    
    /**
     * Validate date format
     */
    public static function validate_date($date, $format = 'Y-m-d') {
        $d = DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }
    
    /**
     * Validate file upload
     */
    public static function validate_file_upload($file, $allowed_types = array(), $max_size = 0) {
        $errors = array();
        
        if (!isset($file['error']) || is_array($file['error'])) {
            $errors[] = __('Invalid file upload.', 'cod-clan-league');
            return array('errors' => $errors, 'valid' => false);
        }
        
        switch ($file['error']) {
            case UPLOAD_ERR_OK:
                break;
            case UPLOAD_ERR_NO_FILE:
                $errors[] = __('No file was uploaded.', 'cod-clan-league');
                break;
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                $errors[] = __('File is too large.', 'cod-clan-league');
                break;
            default:
                $errors[] = __('Unknown file upload error.', 'cod-clan-league');
                break;
        }
        
        if (!empty($errors)) {
            return array('errors' => $errors, 'valid' => false);
        }
        
        // Check file size
        if ($max_size > 0 && $file['size'] > $max_size) {
            $errors[] = sprintf(__('File size must be less than %s.', 'cod-clan-league'), size_format($max_size));
        }
        
        // Check file type
        if (!empty($allowed_types)) {
            $finfo = new finfo(FILEINFO_MIME_TYPE);
            $mime_type = $finfo->file($file['tmp_name']);
            
            if (!in_array($mime_type, $allowed_types)) {
                $errors[] = __('Invalid file type.', 'cod-clan-league');
            }
        }
        
        return array(
            'errors' => $errors,
            'valid' => empty($errors)
        );
    }
    
    /**
     * Sanitize shortcode attributes
     */
    public static function sanitize_shortcode_atts($atts, $defaults = array()) {
        $sanitized = array();
        
        foreach ($atts as $key => $value) {
            switch ($key) {
                case 'season_id':
                case 'clan_id':
                case 'match_id':
                case 'team_id':
                case 'limit':
                case 'match_limit':
                    $sanitized[$key] = intval($value);
                    break;
                    
                case 'status':
                case 'orderby':
                case 'order':
                    $sanitized[$key] = sanitize_key($value);
                    break;
                    
                case 'show_logo':
                case 'show_stats':
                case 'show_date':
                case 'show_score':
                case 'show_members':
                case 'show_matches':
                case 'show_teams':
                case 'show_dates':
                    $sanitized[$key] = $value === 'true' ? 'true' : 'false';
                    break;
                    
                default:
                    $sanitized[$key] = sanitize_text_field($value);
                    break;
            }
        }
        
        return wp_parse_args($sanitized, $defaults);
    }
    
    /**
     * Check if user can perform action on resource
     */
    public static function user_can_access_resource($user_id, $resource_type, $resource_id, $action) {
        $user = get_user_by('id', $user_id);
        if (!$user) {
            return false;
        }
        
        switch ($resource_type) {
            case 'clan':
                return self::user_can_access_clan($user_id, $resource_id, $action);
                
            case 'match':
                return self::user_can_access_match($user_id, $resource_id, $action);
                
            case 'season':
                return self::user_can_access_season($user_id, $resource_id, $action);
                
            default:
                return false;
        }
    }
    
    /**
     * Check clan access permissions
     */
    private static function user_can_access_clan($user_id, $clan_id, $action) {
        $user_roles = new COD_Clan_League_User_Roles();
        
        switch ($action) {
            case 'view':
                return true; // Anyone can view clans
                
            case 'edit':
            case 'manage':
                return $user_roles->user_can_manage_clan($user_id, $clan_id);
                
            case 'delete':
                return current_user_can('delete_others_cod_clans') || 
                       ($user_roles->is_user_clan_captain($user_id, $clan_id) && current_user_can('delete_cod_clan'));
                
            default:
                return false;
        }
    }
    
    /**
     * Check match access permissions
     */
    private static function user_can_access_match($user_id, $match_id, $action) {
        $user_roles = new COD_Clan_League_User_Roles();
        
        switch ($action) {
            case 'view':
                return true; // Anyone can view matches
                
            case 'report':
                return $user_roles->user_can_report_match($user_id, $match_id);
                
            case 'approve':
                return current_user_can('approve_cod_results');
                
            case 'override':
                return current_user_can('override_cod_results');
                
            default:
                return false;
        }
    }
    
    /**
     * Check season access permissions
     */
    private static function user_can_access_season($user_id, $season_id, $action) {
        switch ($action) {
            case 'view':
                return true; // Anyone can view seasons
                
            case 'edit':
            case 'manage':
                return current_user_can('manage_cod_seasons');
                
            case 'delete':
                return current_user_can('delete_others_cod_seasons');
                
            default:
                return false;
        }
    }
    
    /**
     * Rate limiting for AJAX requests
     */
    public static function check_rate_limit($action, $user_id = null, $limit = 10, $window = 60) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        $transient_key = "cod_rate_limit_{$action}_{$user_id}";
        $requests = get_transient($transient_key);
        
        if ($requests === false) {
            $requests = 1;
            set_transient($transient_key, $requests, $window);
            return true;
        }
        
        if ($requests >= $limit) {
            return false;
        }
        
        set_transient($transient_key, $requests + 1, $window);
        return true;
    }
    
    /**
     * Log security events
     */
    public static function log_security_event($event, $user_id = null, $details = array()) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        $log_entry = array(
            'timestamp' => current_time('mysql'),
            'event' => $event,
            'user_id' => $user_id,
            'ip_address' => self::get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'details' => $details,
        );
        
        // Store in WordPress options or custom table
        $logs = get_option('cod_clan_league_security_logs', array());
        $logs[] = $log_entry;
        
        // Keep only last 100 entries
        if (count($logs) > 100) {
            $logs = array_slice($logs, -100);
        }
        
        update_option('cod_clan_league_security_logs', $logs);
        
        // Also log to WordPress error log if it's a critical event
        if (in_array($event, array('unauthorized_access', 'failed_permission_check', 'suspicious_activity'))) {
            error_log("COD Clan League Security: {$event} - User ID: {$user_id} - IP: " . self::get_client_ip());
        }
    }
    
    /**
     * Get client IP address
     */
    private static function get_client_ip() {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
}
